import React from 'react';
import QuantumWorkflowDashboard from '../components/QuantumWorkflowDashboard';

const QuantumWorkflowPage: React.FC = () => {
  return (
    <div className="quantum-workflow-page">
      <div className="page-header">
        <h1>⚛️ Kontour Quantum Workflow Laboratory</h1>
        <p>Advanced quantum computing workflows for cryptocurrency platform enhancement</p>
      </div>
      
      <QuantumWorkflowDashboard />
      
      <style>{`
        .quantum-workflow-page {
          min-height: 100vh;
          background: linear-gradient(135deg, #1e1b4b 0%, #312e81 50%, #581c87 100%);
        }
        
        .page-header {
          padding: 40px 20px 20px 20px;
          text-align: center;
          color: white;
        }
        
        .page-header h1 {
          font-size: 2.5rem;
          margin-bottom: 10px;
          text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }
        
        .page-header p {
          font-size: 1.2rem;
          opacity: 0.9;
          max-width: 800px;
          margin: 0 auto;
        }
      `}</style>
    </div>
  );
};

export default QuantumWorkflowPage;
