"""
Kontour Coin Complete Backend Workflow Service
FastAPI service for managing complete backend workflows
"""

import asyncio
import json
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional
from fastapi import FastAPI, HTTPException, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import uvicorn

from kontour_backend_orchestrator import (
    KontourBackendOrchestrator, WorkflowType, WorkflowStatus
)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Pydantic models
class WorkflowCreateRequest(BaseModel):
    workflow_type: str
    name: str
    description: str
    metadata: Optional[Dict[str, Any]] = None

class WorkflowResponse(BaseModel):
    workflow_id: str
    status: str
    message: str

class HealthResponse(BaseModel):
    status: str
    service: str
    timestamp: str
    active_workflows: int
    total_workflows: int
    service_health: Dict[str, Any]

# Initialize FastAPI app
app = FastAPI(
    title="Kontour Backend Workflow Service",
    description="Complete backend workflow orchestration service",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Global orchestrator
orchestrator: Optional[KontourBackendOrchestrator] = None

@app.on_event("startup")
async def startup_event():
    """Initialize Backend Orchestrator on startup"""
    global orchestrator
    
    logger.info("Initializing Kontour Backend Workflow Service...")
    
    try:
        orchestrator = KontourBackendOrchestrator()
        
        # Start orchestrator in background
        asyncio.create_task(orchestrator.start_orchestrator())
        
        logger.info("✅ Backend Workflow Service initialized successfully")
        
    except Exception as e:
        logger.error(f"❌ Failed to initialize Backend Workflow Service: {e}")
        raise

@app.get("/health", response_model=HealthResponse)
async def health_check():
    """Health check endpoint"""
    global orchestrator
    
    if not orchestrator:
        raise HTTPException(status_code=503, detail="Orchestrator not initialized")
    
    workflows = orchestrator.get_all_workflows()
    active_workflows = len([w for w in workflows if w['status'] == 'running'])
    service_health = orchestrator.get_service_health()
    
    return HealthResponse(
        status="healthy",
        service="backend-workflow-service",
        timestamp=datetime.now().isoformat(),
        active_workflows=active_workflows,
        total_workflows=len(workflows),
        service_health=service_health
    )

@app.post("/workflows", response_model=WorkflowResponse)
async def create_workflow(request: WorkflowCreateRequest):
    """Create a new workflow"""
    global orchestrator
    
    if not orchestrator:
        raise HTTPException(status_code=503, detail="Orchestrator not initialized")
    
    try:
        # Validate workflow type
        try:
            workflow_type = WorkflowType(request.workflow_type)
        except ValueError:
            raise HTTPException(status_code=400, detail=f"Invalid workflow type: {request.workflow_type}")
        
        # Create workflow
        workflow_id = await orchestrator.create_workflow(
            workflow_type=workflow_type,
            name=request.name,
            description=request.description,
            created_by="api_user",
            metadata=request.metadata
        )
        
        # Start workflow
        success = await orchestrator.start_workflow(workflow_id)
        
        if success:
            return WorkflowResponse(
                workflow_id=workflow_id,
                status="started",
                message="Workflow created and started successfully"
            )
        else:
            raise HTTPException(status_code=500, detail="Failed to start workflow")
            
    except Exception as e:
        logger.error(f"Failed to create workflow: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/workflows")
async def list_workflows():
    """List all workflows"""
    global orchestrator
    
    if not orchestrator:
        raise HTTPException(status_code=503, detail="Orchestrator not initialized")
    
    return orchestrator.get_all_workflows()

@app.get("/workflows/{workflow_id}")
async def get_workflow(workflow_id: str):
    """Get specific workflow details"""
    global orchestrator
    
    if not orchestrator:
        raise HTTPException(status_code=503, detail="Orchestrator not initialized")
    
    workflow_status = orchestrator.get_workflow_status(workflow_id)
    
    if not workflow_status:
        raise HTTPException(status_code=404, detail="Workflow not found")
    
    return workflow_status

@app.post("/workflows/{workflow_id}/cancel")
async def cancel_workflow(workflow_id: str):
    """Cancel a workflow"""
    global orchestrator
    
    if not orchestrator:
        raise HTTPException(status_code=503, detail="Orchestrator not initialized")
    
    success = await orchestrator.cancel_workflow(workflow_id)
    
    if success:
        return {"status": "cancelled", "message": f"Workflow {workflow_id} cancelled successfully"}
    else:
        raise HTTPException(status_code=404, detail="Workflow not found")

@app.get("/workflows/types")
async def get_workflow_types():
    """Get available workflow types"""
    return {
        "workflow_types": [
            {
                "type": wf_type.value,
                "name": wf_type.value.replace('_', ' ').title(),
                "description": f"{wf_type.value.replace('_', ' ').title()} workflow"
            }
            for wf_type in WorkflowType
        ]
    }

@app.get("/services/health")
async def get_service_health():
    """Get health status of all services"""
    global orchestrator
    
    if not orchestrator:
        raise HTTPException(status_code=503, detail="Orchestrator not initialized")
    
    return orchestrator.get_service_health()

@app.post("/workflows/trading")
async def create_trading_workflow(
    symbols: List[str] = ["BTC", "ETH"],
    trade_type: str = "market_analysis",
    amount: Optional[float] = None
):
    """Create a trading workflow"""
    global orchestrator
    
    if not orchestrator:
        raise HTTPException(status_code=503, detail="Orchestrator not initialized")
    
    try:
        metadata = {
            "symbols": symbols,
            "trade_type": trade_type,
            "amount": amount,
            "timestamp": datetime.now().isoformat()
        }
        
        workflow_id = await orchestrator.create_workflow(
            workflow_type=WorkflowType.TRADING,
            name=f"Trading Workflow - {', '.join(symbols)}",
            description=f"Automated trading workflow for {', '.join(symbols)}",
            created_by="trading_api",
            metadata=metadata
        )
        
        await orchestrator.start_workflow(workflow_id)
        
        return {
            "workflow_id": workflow_id,
            "status": "started",
            "symbols": symbols,
            "trade_type": trade_type
        }
        
    except Exception as e:
        logger.error(f"Failed to create trading workflow: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/workflows/ai-analysis")
async def create_ai_analysis_workflow(
    query: str,
    sources: List[str] = ["market_data", "news", "social_sentiment"],
    analysis_type: str = "comprehensive"
):
    """Create an AI analysis workflow"""
    global orchestrator
    
    if not orchestrator:
        raise HTTPException(status_code=503, detail="Orchestrator not initialized")
    
    try:
        metadata = {
            "query": query,
            "sources": sources,
            "analysis_type": analysis_type,
            "timestamp": datetime.now().isoformat()
        }
        
        workflow_id = await orchestrator.create_workflow(
            workflow_type=WorkflowType.AI_ANALYSIS,
            name=f"AI Analysis - {query[:50]}...",
            description=f"AI analysis workflow for: {query}",
            created_by="ai_api",
            metadata=metadata
        )
        
        await orchestrator.start_workflow(workflow_id)
        
        return {
            "workflow_id": workflow_id,
            "status": "started",
            "query": query,
            "sources": sources
        }
        
    except Exception as e:
        logger.error(f"Failed to create AI analysis workflow: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/workflows/quantum")
async def create_quantum_workflow(
    algorithm: str,
    parameters: Dict[str, Any] = None,
    quantum_backend: str = "simulator"
):
    """Create a quantum computation workflow"""
    global orchestrator
    
    if not orchestrator:
        raise HTTPException(status_code=503, detail="Orchestrator not initialized")
    
    try:
        metadata = {
            "algorithm": algorithm,
            "parameters": parameters or {},
            "quantum_backend": quantum_backend,
            "timestamp": datetime.now().isoformat()
        }
        
        workflow_id = await orchestrator.create_workflow(
            workflow_type=WorkflowType.QUANTUM_COMPUTATION,
            name=f"Quantum Workflow - {algorithm}",
            description=f"Quantum computation workflow for {algorithm}",
            created_by="quantum_api",
            metadata=metadata
        )
        
        await orchestrator.start_workflow(workflow_id)
        
        return {
            "workflow_id": workflow_id,
            "status": "started",
            "algorithm": algorithm,
            "backend": quantum_backend
        }
        
    except Exception as e:
        logger.error(f"Failed to create quantum workflow: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/workflows/compliance")
async def create_compliance_workflow(
    user_id: str,
    check_type: str = "full_verification",
    priority: str = "normal"
):
    """Create a compliance check workflow"""
    global orchestrator
    
    if not orchestrator:
        raise HTTPException(status_code=503, detail="Orchestrator not initialized")
    
    try:
        metadata = {
            "user_id": user_id,
            "check_type": check_type,
            "priority": priority,
            "timestamp": datetime.now().isoformat()
        }
        
        workflow_id = await orchestrator.create_workflow(
            workflow_type=WorkflowType.COMPLIANCE_CHECK,
            name=f"Compliance Check - {user_id}",
            description=f"Compliance verification workflow for user {user_id}",
            created_by="compliance_api",
            metadata=metadata
        )
        
        await orchestrator.start_workflow(workflow_id)
        
        return {
            "workflow_id": workflow_id,
            "status": "started",
            "user_id": user_id,
            "check_type": check_type
        }
        
    except Exception as e:
        logger.error(f"Failed to create compliance workflow: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/workflows/security")
async def create_security_workflow(
    scan_targets: List[str],
    scan_type: str = "comprehensive",
    priority: str = "high"
):
    """Create a cybersecurity scan workflow"""
    global orchestrator
    
    if not orchestrator:
        raise HTTPException(status_code=503, detail="Orchestrator not initialized")
    
    try:
        metadata = {
            "targets": scan_targets,
            "scan_type": scan_type,
            "priority": priority,
            "timestamp": datetime.now().isoformat()
        }
        
        workflow_id = await orchestrator.create_workflow(
            workflow_type=WorkflowType.CYBERSECURITY_SCAN,
            name=f"Security Scan - {len(scan_targets)} targets",
            description=f"Cybersecurity scan workflow for {len(scan_targets)} targets",
            created_by="security_api",
            metadata=metadata
        )
        
        await orchestrator.start_workflow(workflow_id)
        
        return {
            "workflow_id": workflow_id,
            "status": "started",
            "targets": scan_targets,
            "scan_type": scan_type
        }
        
    except Exception as e:
        logger.error(f"Failed to create security workflow: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/metrics")
async def get_metrics():
    """Get workflow metrics"""
    global orchestrator
    
    if not orchestrator:
        raise HTTPException(status_code=503, detail="Orchestrator not initialized")
    
    workflows = orchestrator.get_all_workflows()
    service_health = orchestrator.get_service_health()
    
    # Calculate metrics
    total_workflows = len(workflows)
    running_workflows = len([w for w in workflows if w['status'] == 'running'])
    completed_workflows = len([w for w in workflows if w['status'] == 'completed'])
    failed_workflows = len([w for w in workflows if w['status'] == 'failed'])
    
    success_rate = (completed_workflows / total_workflows * 100) if total_workflows > 0 else 0
    
    return {
        "total_workflows": total_workflows,
        "running_workflows": running_workflows,
        "completed_workflows": completed_workflows,
        "failed_workflows": failed_workflows,
        "success_rate": round(success_rate, 2),
        "healthy_services": service_health.get("healthy_services", 0),
        "total_services": service_health.get("total_services", 0),
        "timestamp": datetime.now().isoformat()
    }

@app.post("/demo")
async def run_demo():
    """Run a demonstration of workflow capabilities"""
    global orchestrator
    
    if not orchestrator:
        raise HTTPException(status_code=503, detail="Orchestrator not initialized")
    
    try:
        demo_workflows = []
        
        # Create demo workflows
        demo_configs = [
            {
                "type": WorkflowType.TRADING,
                "name": "Demo Trading Analysis",
                "description": "Demonstration trading workflow",
                "metadata": {"symbols": ["BTC", "ETH"], "demo": True}
            },
            {
                "type": WorkflowType.AI_ANALYSIS,
                "name": "Demo AI Analysis",
                "description": "Demonstration AI analysis workflow",
                "metadata": {"query": "Analyze current market conditions", "demo": True}
            },
            {
                "type": WorkflowType.COMPLIANCE_CHECK,
                "name": "Demo Compliance Check",
                "description": "Demonstration compliance workflow",
                "metadata": {"user_id": "demo_user", "demo": True}
            }
        ]
        
        for config in demo_configs:
            workflow_id = await orchestrator.create_workflow(
                workflow_type=config["type"],
                name=config["name"],
                description=config["description"],
                created_by="demo",
                metadata=config["metadata"]
            )
            
            await orchestrator.start_workflow(workflow_id)
            demo_workflows.append(workflow_id)
            
            # Small delay between workflows
            await asyncio.sleep(1)
        
        return {
            "status": "demo_started",
            "workflows": demo_workflows,
            "message": "Demo workflows created and started successfully",
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Demo failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8100, log_level="info")
