import React, { useState, useEffect } from 'react';
import { useKontour } from '../contexts/KontourContext';
import { useRealtime } from '../contexts/RealtimeContext';
import RealTimeTradingEngine from '../components/RealTimeTradingEngine';
import EnhancedTradingWorkflow from '../components/EnhancedTradingWorkflow';

interface TradingData {
  price: number;
  volume: number;
  change24h: number;
  marketCap: number;
  timestamp: number;
}

interface OrderBook {
  bids: Array<{ price: number; amount: number }>;
  asks: Array<{ price: number; amount: number }>;
}

const TradingDashboardPage: React.FC = () => {
  const { kontourData } = useKontour();
  const { realtimeData } = useRealtime();
  const [tradingData, setTradingData] = useState<TradingData>({
    price: 0,
    volume: 0,
    change24h: 0,
    marketCap: 0,
    timestamp: Date.now()
  });
  const [orderBook, setOrderBook] = useState<OrderBook>({ bids: [], asks: [] });
  const [activeTab, setActiveTab] = useState<'spot' | 'futures' | 'options'>('spot');

  useEffect(() => {
    // Simulate real-time trading data updates
    const interval = setInterval(() => {
      setTradingData(prev => ({
        ...prev,
        price: prev.price + (Math.random() - 0.5) * 0.1,
        volume: Math.random() * 1000000,
        change24h: (Math.random() - 0.5) * 10,
        timestamp: Date.now()
      }));

      // Update order book
      setOrderBook({
        bids: Array.from({ length: 10 }, (_, i) => ({
          price: tradingData.price - (i + 1) * 0.01,
          amount: Math.random() * 1000
        })),
        asks: Array.from({ length: 10 }, (_, i) => ({
          price: tradingData.price + (i + 1) * 0.01,
          amount: Math.random() * 1000
        }))
      });
    }, 1000);

    return () => clearInterval(interval);
  }, [tradingData.price]);

  return (
    <div className="trading-dashboard">
      <div className="dashboard-header">
        <div className="header-content">
          <h1>💎 Kontour Coin Trading Dashboard</h1>
          <div className="price-ticker">
            <span className="price">${tradingData.price.toFixed(4)}</span>
            <span className={`change ${tradingData.change24h >= 0 ? 'positive' : 'negative'}`}>
              {tradingData.change24h >= 0 ? '+' : ''}{tradingData.change24h.toFixed(2)}%
            </span>
          </div>
        </div>
      </div>

      <div className="trading-tabs">
        <button 
          className={`tab ${activeTab === 'spot' ? 'active' : ''}`}
          onClick={() => setActiveTab('spot')}
        >
          Spot Trading
        </button>
        <button 
          className={`tab ${activeTab === 'futures' ? 'active' : ''}`}
          onClick={() => setActiveTab('futures')}
        >
          Futures
        </button>
        <button 
          className={`tab ${activeTab === 'options' ? 'active' : ''}`}
          onClick={() => setActiveTab('options')}
        >
          Options
        </button>
      </div>

      <div className="trading-content">
        <div className="trading-main">
          <div className="chart-section">
            <RealTimeTradingEngine />
          </div>
          
          <div className="order-book">
            <h3>Order Book</h3>
            <div className="order-book-content">
              <div className="asks">
                <h4>Asks</h4>
                {orderBook.asks.map((ask, index) => (
                  <div key={index} className="order-row ask">
                    <span className="price">{ask.price.toFixed(4)}</span>
                    <span className="amount">{ask.amount.toFixed(2)}</span>
                  </div>
                ))}
              </div>
              <div className="spread">
                <span>Spread: {orderBook.asks[0] && orderBook.bids[0] ? 
                  (orderBook.asks[0].price - orderBook.bids[0].price).toFixed(4) : '0.0000'}</span>
              </div>
              <div className="bids">
                <h4>Bids</h4>
                {orderBook.bids.map((bid, index) => (
                  <div key={index} className="order-row bid">
                    <span className="price">{bid.price.toFixed(4)}</span>
                    <span className="amount">{bid.amount.toFixed(2)}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>

        <div className="trading-sidebar">
          <div className="trading-form">
            <h3>Place Order</h3>
            <div className="order-type-tabs">
              <button className="active">Market</button>
              <button>Limit</button>
              <button>Stop</button>
            </div>
            
            <div className="order-form">
              <div className="form-group">
                <label>Amount</label>
                <input type="number" placeholder="0.00" />
              </div>
              <div className="form-group">
                <label>Price</label>
                <input type="number" placeholder={tradingData.price.toFixed(4)} />
              </div>
              <div className="form-group">
                <label>Total</label>
                <input type="number" placeholder="0.00" />
              </div>
              
              <div className="order-buttons">
                <button className="buy-button">Buy KNTOUR</button>
                <button className="sell-button">Sell KNTOUR</button>
              </div>
            </div>
          </div>

          <div className="market-stats">
            <h3>Market Statistics</h3>
            <div className="stat-item">
              <span>24h Volume</span>
              <span>{tradingData.volume.toLocaleString()}</span>
            </div>
            <div className="stat-item">
              <span>Market Cap</span>
              <span>${(tradingData.price * 1000000).toLocaleString()}</span>
            </div>
            <div className="stat-item">
              <span>Circulating Supply</span>
              <span>1,000,000 KNTOUR</span>
            </div>
          </div>
        </div>
      </div>

      <div className="enhanced-trading-section">
        <EnhancedTradingWorkflow />
      </div>

      <style jsx>{`
        .trading-dashboard {
          padding: 20px;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          min-height: 100vh;
          color: white;
        }

        .dashboard-header {
          margin-bottom: 30px;
        }

        .header-content {
          display: flex;
          justify-content: space-between;
          align-items: center;
        }

        .price-ticker {
          display: flex;
          align-items: center;
          gap: 15px;
        }

        .price {
          font-size: 2rem;
          font-weight: bold;
        }

        .change.positive {
          color: #4ade80;
        }

        .change.negative {
          color: #f87171;
        }

        .trading-tabs {
          display: flex;
          gap: 10px;
          margin-bottom: 20px;
        }

        .tab {
          padding: 10px 20px;
          background: rgba(255, 255, 255, 0.1);
          border: none;
          border-radius: 8px;
          color: white;
          cursor: pointer;
          transition: all 0.3s ease;
        }

        .tab.active {
          background: rgba(255, 255, 255, 0.2);
          transform: translateY(-2px);
        }

        .trading-content {
          display: grid;
          grid-template-columns: 1fr 300px;
          gap: 20px;
        }

        .trading-main {
          display: grid;
          grid-template-rows: 1fr auto;
          gap: 20px;
        }

        .chart-section {
          background: rgba(255, 255, 255, 0.1);
          border-radius: 12px;
          padding: 20px;
          backdrop-filter: blur(10px);
        }

        .order-book {
          background: rgba(255, 255, 255, 0.1);
          border-radius: 12px;
          padding: 20px;
          backdrop-filter: blur(10px);
        }

        .order-book-content {
          display: grid;
          grid-template-rows: 1fr auto 1fr;
          gap: 10px;
          max-height: 400px;
        }

        .order-row {
          display: flex;
          justify-content: space-between;
          padding: 5px 0;
          font-family: monospace;
        }

        .ask {
          color: #f87171;
        }

        .bid {
          color: #4ade80;
        }

        .spread {
          text-align: center;
          padding: 10px;
          background: rgba(255, 255, 255, 0.1);
          border-radius: 8px;
        }

        .trading-sidebar {
          display: flex;
          flex-direction: column;
          gap: 20px;
        }

        .trading-form, .market-stats {
          background: rgba(255, 255, 255, 0.1);
          border-radius: 12px;
          padding: 20px;
          backdrop-filter: blur(10px);
        }

        .order-type-tabs {
          display: flex;
          gap: 5px;
          margin-bottom: 20px;
        }

        .order-type-tabs button {
          flex: 1;
          padding: 8px;
          background: rgba(255, 255, 255, 0.1);
          border: none;
          border-radius: 6px;
          color: white;
          cursor: pointer;
        }

        .order-type-tabs button.active {
          background: rgba(255, 255, 255, 0.2);
        }

        .form-group {
          margin-bottom: 15px;
        }

        .form-group label {
          display: block;
          margin-bottom: 5px;
          font-weight: 500;
        }

        .form-group input {
          width: 100%;
          padding: 10px;
          background: rgba(255, 255, 255, 0.1);
          border: 1px solid rgba(255, 255, 255, 0.2);
          border-radius: 6px;
          color: white;
        }

        .form-group input::placeholder {
          color: rgba(255, 255, 255, 0.6);
        }

        .order-buttons {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 10px;
          margin-top: 20px;
        }

        .buy-button, .sell-button {
          padding: 12px;
          border: none;
          border-radius: 8px;
          font-weight: bold;
          cursor: pointer;
          transition: all 0.3s ease;
        }

        .buy-button {
          background: #4ade80;
          color: white;
        }

        .sell-button {
          background: #f87171;
          color: white;
        }

        .buy-button:hover, .sell-button:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        }

        .stat-item {
          display: flex;
          justify-content: space-between;
          padding: 10px 0;
          border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .enhanced-trading-section {
          margin-top: 30px;
        }

        @media (max-width: 768px) {
          .trading-content {
            grid-template-columns: 1fr;
          }
          
          .trading-main {
            grid-template-rows: auto auto;
          }
        }
      `}</style>
    </div>
  );
};

export default TradingDashboardPage;
