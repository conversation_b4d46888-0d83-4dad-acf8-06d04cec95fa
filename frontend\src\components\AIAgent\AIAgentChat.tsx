import React, { useState, useEffect, useRef } from 'react';
import {
  Box,
  Card,
  CardContent,
  TextField,
  Button,
  Typography,
  Avatar,
  Chip,
  LinearProgress,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Grid,
  Paper,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Alert,
  Tooltip,
  IconButton
} from '@mui/material';
import {
  Send as SendIcon,
  SmartToy as BotIcon,
  Person as PersonIcon,
  ExpandMore as ExpandMoreIcon,
  Psychology as PsychologyIcon,
  TrendingUp as TrendingUpIcon,
  Security as SecurityIcon,
  Analytics as AnalyticsIcon,
  Refresh as RefreshIcon,
  Settings as SettingsIcon
} from '@mui/icons-material';
import { styled } from '@mui/material/styles';

// Styled components
const ChatContainer = styled(Box)(({ theme }) => ({
  height: '600px',
  display: 'flex',
  flexDirection: 'column',
  backgroundColor: theme.palette.background.paper,
  borderRadius: theme.shape.borderRadius,
  overflow: 'hidden'
}));

const MessagesContainer = styled(Box)(({ theme }) => ({
  flex: 1,
  overflowY: 'auto',
  padding: theme.spacing(1),
  backgroundColor: theme.palette.background.default
}));

const MessageBubble = styled(Paper)<{ isUser: boolean }>(({ theme, isUser }) => ({
  padding: theme.spacing(1, 2),
  margin: theme.spacing(1, 0),
  maxWidth: '80%',
  alignSelf: isUser ? 'flex-end' : 'flex-start',
  backgroundColor: isUser ? theme.palette.primary.main : theme.palette.grey[100],
  color: isUser ? theme.palette.primary.contrastText : theme.palette.text.primary,
  borderRadius: theme.spacing(2),
  marginLeft: isUser ? 'auto' : 0,
  marginRight: isUser ? 0 : 'auto'
}));

const InputContainer = styled(Box)(({ theme }) => ({
  padding: theme.spacing(2),
  borderTop: `1px solid ${theme.palette.divider}`,
  backgroundColor: theme.palette.background.paper
}));

// Types
interface Message {
  id: string;
  content: string;
  isUser: boolean;
  timestamp: Date;
  agentId?: string;
  confidence?: number;
  recommendations?: string[];
  reasoning?: string;
  modelUsed?: string;
  processingTime?: number;
}

interface Agent {
  agent_id: string;
  name: string;
  role: string;
  llm_provider: string;
  enabled: boolean;
  performance: {
    total_requests: number;
    successful_responses: number;
    success_rate: number;
    average_response_time: number;
  };
}

interface ConsensusResponse {
  type: string;
  consensus_id?: string;
  response: string;
  confidence: number;
  agreement_level?: number;
  recommendations: string[];
  participating_agents?: string[];
  individual_responses?: Array<{
    agent_id: string;
    content: string;
    confidence: number;
    model_used: string;
  }>;
}

const AIAgentChat: React.FC = () => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputMessage, setInputMessage] = useState('');
  const [selectedAgent, setSelectedAgent] = useState<string>('consensus');
  const [agents, setAgents] = useState<Agent[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Scroll to bottom when new messages arrive
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // Load agents on component mount
  useEffect(() => {
    loadAgents();
    // Add welcome message
    setMessages([{
      id: '1',
      content: 'Welcome to Kontour Coin AI Agent! I can help you with trading advice, market analysis, risk management, and more. Ask me anything about cryptocurrency!',
      isUser: false,
      timestamp: new Date(),
      agentId: 'system',
      confidence: 1.0
    }]);
  }, []);

  const loadAgents = async () => {
    try {
      const response = await fetch('http://localhost:8025/agents');
      if (response.ok) {
        const agentsData = await response.json();
        const agentsList = Object.entries(agentsData).map(([id, data]: [string, any]) => ({
          agent_id: id,
          ...data
        }));
        setAgents(agentsList);
      }
    } catch (error) {
      console.error('Failed to load agents:', error);
    }
  };

  const sendMessage = async () => {
    if (!inputMessage.trim()) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      content: inputMessage,
      isUser: true,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputMessage('');
    setLoading(true);
    setError(null);

    try {
      const requestBody = {
        message: inputMessage,
        user_id: 'user_' + Date.now(),
        agent_id: selectedAgent === 'consensus' ? null : selectedAgent,
        context: {
          timestamp: new Date().toISOString(),
          session_id: 'session_' + Date.now()
        }
      };

      const response = await fetch('http://localhost:8025/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestBody)
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data: ConsensusResponse = await response.json();

      const botMessage: Message = {
        id: (Date.now() + 1).toString(),
        content: data.response,
        isUser: false,
        timestamp: new Date(),
        agentId: data.type === 'consensus' ? 'consensus' : selectedAgent,
        confidence: data.confidence,
        recommendations: data.recommendations,
        reasoning: data.type === 'single_agent' ? (data as any).reasoning : undefined,
        modelUsed: data.type === 'single_agent' ? (data as any).model_used : undefined,
        processingTime: data.type === 'single_agent' ? (data as any).processing_time : undefined
      };

      setMessages(prev => [...prev, botMessage]);

    } catch (error) {
      console.error('Error sending message:', error);
      setError('Failed to send message. Please try again.');
      
      const errorMessage: Message = {
        id: (Date.now() + 1).toString(),
        content: 'Sorry, I encountered an error processing your request. Please try again.',
        isUser: false,
        timestamp: new Date(),
        agentId: 'error'
      };
      
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setLoading(false);
    }
  };

  const handleKeyPress = (event: React.KeyboardEvent) => {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault();
      sendMessage();
    }
  };

  const getAgentIcon = (agentId: string) => {
    if (agentId === 'consensus') return <PsychologyIcon />;
    if (agentId?.includes('trading')) return <TrendingUpIcon />;
    if (agentId?.includes('risk')) return <SecurityIcon />;
    if (agentId?.includes('analyst')) return <AnalyticsIcon />;
    return <BotIcon />;
  };

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 0.8) return 'success';
    if (confidence >= 0.6) return 'warning';
    return 'error';
  };

  return (
    <Box sx={{ p: 2 }}>
      <Grid container spacing={3}>
        {/* Agent Selection */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                <Typography variant="h5" component="h2">
                  💎 Kontour AI Agent Chat
                </Typography>
                <Tooltip title="Refresh agents">
                  <IconButton onClick={loadAgents}>
                    <RefreshIcon />
                  </IconButton>
                </Tooltip>
              </Box>
              
              <FormControl fullWidth sx={{ mb: 2 }}>
                <InputLabel>Select AI Agent</InputLabel>
                <Select
                  value={selectedAgent}
                  onChange={(e) => setSelectedAgent(e.target.value)}
                  label="Select AI Agent"
                >
                  <MenuItem value="consensus">
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <PsychologyIcon />
                      <Box>
                        <Typography variant="body2">AI Consensus</Typography>
                        <Typography variant="caption" color="textSecondary">
                          Get insights from multiple specialized agents
                        </Typography>
                      </Box>
                    </Box>
                  </MenuItem>
                  {agents.filter(agent => agent.enabled).map((agent) => (
                    <MenuItem key={agent.agent_id} value={agent.agent_id}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        {getAgentIcon(agent.agent_id)}
                        <Box>
                          <Typography variant="body2">{agent.name}</Typography>
                          <Typography variant="caption" color="textSecondary">
                            {agent.role.replace('_', ' ')} • {agent.llm_provider}
                          </Typography>
                        </Box>
                      </Box>
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>

              {error && (
                <Alert severity="error" sx={{ mb: 2 }}>
                  {error}
                </Alert>
              )}
            </CardContent>
          </Card>
        </Grid>

        {/* Chat Interface */}
        <Grid item xs={12} md={8}>
          <Card>
            <ChatContainer>
              <MessagesContainer>
                {messages.map((message) => (
                  <Box key={message.id} sx={{ display: 'flex', flexDirection: 'column' }}>
                    <MessageBubble isUser={message.isUser} elevation={1}>
                      <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 1 }}>
                        <Avatar sx={{ width: 32, height: 32 }}>
                          {message.isUser ? <PersonIcon /> : getAgentIcon(message.agentId || '')}
                        </Avatar>
                        <Box sx={{ flex: 1 }}>
                          <Typography variant="body1" sx={{ mb: 1 }}>
                            {message.content}
                          </Typography>
                          
                          {message.confidence && (
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mt: 1 }}>
                              <Chip
                                label={`Confidence: ${(message.confidence * 100).toFixed(0)}%`}
                                size="small"
                                color={getConfidenceColor(message.confidence) as any}
                              />
                              {message.modelUsed && (
                                <Chip
                                  label={message.modelUsed}
                                  size="small"
                                  variant="outlined"
                                />
                              )}
                            </Box>
                          )}
                          
                          {message.recommendations && message.recommendations.length > 0 && (
                            <Accordion sx={{ mt: 1 }}>
                              <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                                <Typography variant="body2">
                                  Recommendations ({message.recommendations.length})
                                </Typography>
                              </AccordionSummary>
                              <AccordionDetails>
                                {message.recommendations.map((rec, index) => (
                                  <Typography key={index} variant="body2" sx={{ mb: 1 }}>
                                    • {rec}
                                  </Typography>
                                ))}
                              </AccordionDetails>
                            </Accordion>
                          )}
                        </Box>
                      </Box>
                    </MessageBubble>
                  </Box>
                ))}
                {loading && (
                  <Box sx={{ p: 2 }}>
                    <LinearProgress />
                    <Typography variant="body2" color="textSecondary" sx={{ mt: 1 }}>
                      AI Agent is thinking...
                    </Typography>
                  </Box>
                )}
                <div ref={messagesEndRef} />
              </MessagesContainer>

              <InputContainer>
                <Box sx={{ display: 'flex', gap: 1 }}>
                  <TextField
                    fullWidth
                    multiline
                    maxRows={4}
                    value={inputMessage}
                    onChange={(e) => setInputMessage(e.target.value)}
                    onKeyPress={handleKeyPress}
                    placeholder="Ask me about trading, market analysis, risk management..."
                    disabled={loading}
                  />
                  <Button
                    variant="contained"
                    onClick={sendMessage}
                    disabled={loading || !inputMessage.trim()}
                    sx={{ minWidth: 'auto', px: 2 }}
                  >
                    <SendIcon />
                  </Button>
                </Box>
              </InputContainer>
            </ChatContainer>
          </Card>
        </Grid>

        {/* Agent Status Panel */}
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Active Agents
              </Typography>
              {agents.filter(agent => agent.enabled).map((agent) => (
                <Box key={agent.agent_id} sx={{ mb: 2, p: 1, border: 1, borderColor: 'divider', borderRadius: 1 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                    {getAgentIcon(agent.agent_id)}
                    <Typography variant="body2" fontWeight="bold">
                      {agent.name}
                    </Typography>
                  </Box>
                  <Typography variant="caption" color="textSecondary" display="block">
                    {agent.role.replace('_', ' ')} • {agent.llm_provider}
                  </Typography>
                  <Box sx={{ mt: 1 }}>
                    <Typography variant="caption">
                      Success Rate: {(agent.performance.success_rate * 100).toFixed(1)}%
                    </Typography>
                    <LinearProgress
                      variant="determinate"
                      value={agent.performance.success_rate * 100}
                      sx={{ mt: 0.5, height: 4 }}
                    />
                  </Box>
                </Box>
              ))}
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default AIAgentChat;
