import React, { useState, useEffect } from 'react';
import { useKontour } from '../contexts/KontourContext';
import { useRealtime } from '../contexts/RealtimeContext';

interface AnalyticsData {
  networkHealth: number;
  transactionVolume: number;
  activeUsers: number;
  validatorCount: number;
  quantumSecurity: number;
  aiPredictions: {
    priceDirection: 'up' | 'down' | 'stable';
    confidence: number;
    timeframe: string;
  };
  performanceMetrics: {
    tps: number;
    latency: number;
    uptime: number;
  };
}

const AnalyticsDashboardPage: React.FC = () => {
  const { kontourData } = useKontour();
  const { realtimeData } = useRealtime();
  const [analyticsData, setAnalyticsData] = useState<AnalyticsData>({
    networkHealth: 98.5,
    transactionVolume: 1250000,
    activeUsers: 45230,
    validatorCount: 101,
    quantumSecurity: 99.8,
    aiPredictions: {
      priceDirection: 'up',
      confidence: 87.3,
      timeframe: '24h'
    },
    performanceMetrics: {
      tps: 95420,
      latency: 0.3,
      uptime: 99.97
    }
  });

  const [selectedTimeframe, setSelectedTimeframe] = useState<'1h' | '24h' | '7d' | '30d'>('24h');

  useEffect(() => {
    // Simulate real-time analytics updates
    const interval = setInterval(() => {
      setAnalyticsData(prev => ({
        ...prev,
        networkHealth: Math.max(95, Math.min(100, prev.networkHealth + (Math.random() - 0.5) * 2)),
        transactionVolume: prev.transactionVolume + Math.floor(Math.random() * 1000),
        activeUsers: prev.activeUsers + Math.floor((Math.random() - 0.5) * 100),
        performanceMetrics: {
          ...prev.performanceMetrics,
          tps: Math.max(80000, Math.min(100000, prev.performanceMetrics.tps + (Math.random() - 0.5) * 5000)),
          latency: Math.max(0.1, Math.min(1.0, prev.performanceMetrics.latency + (Math.random() - 0.5) * 0.1))
        }
      }));
    }, 2000);

    return () => clearInterval(interval);
  }, []);

  const getHealthColor = (value: number) => {
    if (value >= 95) return '#4ade80';
    if (value >= 85) return '#fbbf24';
    return '#f87171';
  };

  return (
    <div className="analytics-dashboard">
      <div className="dashboard-header">
        <h1>💎 Kontour Coin Analytics Dashboard</h1>
        <div className="timeframe-selector">
          {(['1h', '24h', '7d', '30d'] as const).map(timeframe => (
            <button
              key={timeframe}
              className={`timeframe-btn ${selectedTimeframe === timeframe ? 'active' : ''}`}
              onClick={() => setSelectedTimeframe(timeframe)}
            >
              {timeframe}
            </button>
          ))}
        </div>
      </div>

      <div className="metrics-grid">
        <div className="metric-card">
          <div className="metric-header">
            <h3>Network Health</h3>
            <span className="metric-icon">🌐</span>
          </div>
          <div className="metric-value" style={{ color: getHealthColor(analyticsData.networkHealth) }}>
            {analyticsData.networkHealth.toFixed(1)}%
          </div>
          <div className="metric-trend">
            <span className="trend-indicator positive">↗</span>
            <span>+0.3% from yesterday</span>
          </div>
        </div>

        <div className="metric-card">
          <div className="metric-header">
            <h3>Transaction Volume</h3>
            <span className="metric-icon">💰</span>
          </div>
          <div className="metric-value">
            {analyticsData.transactionVolume.toLocaleString()}
          </div>
          <div className="metric-trend">
            <span className="trend-indicator positive">↗</span>
            <span>+12.5% from yesterday</span>
          </div>
        </div>

        <div className="metric-card">
          <div className="metric-header">
            <h3>Active Users</h3>
            <span className="metric-icon">👥</span>
          </div>
          <div className="metric-value">
            {analyticsData.activeUsers.toLocaleString()}
          </div>
          <div className="metric-trend">
            <span className="trend-indicator positive">↗</span>
            <span>+8.2% from yesterday</span>
          </div>
        </div>

        <div className="metric-card">
          <div className="metric-header">
            <h3>Validator Count</h3>
            <span className="metric-icon">🔒</span>
          </div>
          <div className="metric-value">
            {analyticsData.validatorCount}
          </div>
          <div className="metric-trend">
            <span className="trend-indicator stable">→</span>
            <span>Stable</span>
          </div>
        </div>

        <div className="metric-card">
          <div className="metric-header">
            <h3>Quantum Security</h3>
            <span className="metric-icon">⚛️</span>
          </div>
          <div className="metric-value" style={{ color: getHealthColor(analyticsData.quantumSecurity) }}>
            {analyticsData.quantumSecurity.toFixed(1)}%
          </div>
          <div className="metric-trend">
            <span className="trend-indicator positive">↗</span>
            <span>Enhanced</span>
          </div>
        </div>

        <div className="metric-card">
          <div className="metric-header">
            <h3>TPS Performance</h3>
            <span className="metric-icon">⚡</span>
          </div>
          <div className="metric-value">
            {analyticsData.performanceMetrics.tps.toLocaleString()}
          </div>
          <div className="metric-trend">
            <span className="trend-indicator positive">↗</span>
            <span>Target: 100K TPS</span>
          </div>
        </div>
      </div>

      <div className="analytics-content">
        <div className="ai-predictions-section">
          <div className="section-card">
            <h3>🤖 AI Market Predictions</h3>
            <div className="prediction-content">
              <div className="prediction-main">
                <div className="prediction-direction">
                  <span className={`direction-indicator ${analyticsData.aiPredictions.priceDirection}`}>
                    {analyticsData.aiPredictions.priceDirection === 'up' ? '📈' : 
                     analyticsData.aiPredictions.priceDirection === 'down' ? '📉' : '➡️'}
                  </span>
                  <span className="direction-text">
                    {analyticsData.aiPredictions.priceDirection.toUpperCase()}
                  </span>
                </div>
                <div className="confidence-meter">
                  <div className="confidence-label">Confidence</div>
                  <div className="confidence-bar">
                    <div 
                      className="confidence-fill"
                      style={{ width: `${analyticsData.aiPredictions.confidence}%` }}
                    ></div>
                  </div>
                  <div className="confidence-value">{analyticsData.aiPredictions.confidence}%</div>
                </div>
              </div>
              <div className="prediction-details">
                <div className="detail-item">
                  <span>Timeframe:</span>
                  <span>{analyticsData.aiPredictions.timeframe}</span>
                </div>
                <div className="detail-item">
                  <span>AI Models:</span>
                  <span>GPT-4, Claude, Gemini</span>
                </div>
                <div className="detail-item">
                  <span>Data Sources:</span>
                  <span>Market, Social, On-chain</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="performance-section">
          <div className="section-card">
            <h3>⚡ Performance Metrics</h3>
            <div className="performance-grid">
              <div className="performance-item">
                <div className="performance-label">Transactions/Second</div>
                <div className="performance-value">
                  {analyticsData.performanceMetrics.tps.toLocaleString()}
                </div>
                <div className="performance-bar">
                  <div 
                    className="performance-fill"
                    style={{ width: `${(analyticsData.performanceMetrics.tps / 100000) * 100}%` }}
                  ></div>
                </div>
              </div>
              
              <div className="performance-item">
                <div className="performance-label">Average Latency</div>
                <div className="performance-value">
                  {analyticsData.performanceMetrics.latency.toFixed(2)}ms
                </div>
                <div className="performance-bar">
                  <div 
                    className="performance-fill"
                    style={{ width: `${Math.max(0, 100 - (analyticsData.performanceMetrics.latency * 100))}%` }}
                  ></div>
                </div>
              </div>
              
              <div className="performance-item">
                <div className="performance-label">Network Uptime</div>
                <div className="performance-value">
                  {analyticsData.performanceMetrics.uptime.toFixed(2)}%
                </div>
                <div className="performance-bar">
                  <div 
                    className="performance-fill"
                    style={{ width: `${analyticsData.performanceMetrics.uptime}%` }}
                  ></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <style jsx>{`
        .analytics-dashboard {
          padding: 20px;
          background: linear-gradient(135deg, #1e3a8a 0%, #3730a3 50%, #581c87 100%);
          min-height: 100vh;
          color: white;
        }

        .dashboard-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 30px;
        }

        .timeframe-selector {
          display: flex;
          gap: 10px;
        }

        .timeframe-btn {
          padding: 8px 16px;
          background: rgba(255, 255, 255, 0.1);
          border: none;
          border-radius: 6px;
          color: white;
          cursor: pointer;
          transition: all 0.3s ease;
        }

        .timeframe-btn.active {
          background: rgba(255, 255, 255, 0.2);
          transform: translateY(-2px);
        }

        .metrics-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
          gap: 20px;
          margin-bottom: 30px;
        }

        .metric-card {
          background: rgba(255, 255, 255, 0.1);
          border-radius: 12px;
          padding: 20px;
          backdrop-filter: blur(10px);
          border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .metric-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 15px;
        }

        .metric-header h3 {
          margin: 0;
          font-size: 1rem;
          opacity: 0.9;
        }

        .metric-icon {
          font-size: 1.5rem;
        }

        .metric-value {
          font-size: 2rem;
          font-weight: bold;
          margin-bottom: 10px;
        }

        .metric-trend {
          display: flex;
          align-items: center;
          gap: 8px;
          font-size: 0.9rem;
          opacity: 0.8;
        }

        .trend-indicator {
          font-weight: bold;
        }

        .trend-indicator.positive {
          color: #4ade80;
        }

        .trend-indicator.negative {
          color: #f87171;
        }

        .trend-indicator.stable {
          color: #fbbf24;
        }

        .analytics-content {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 20px;
        }

        .section-card {
          background: rgba(255, 255, 255, 0.1);
          border-radius: 12px;
          padding: 20px;
          backdrop-filter: blur(10px);
          border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .section-card h3 {
          margin: 0 0 20px 0;
          font-size: 1.2rem;
        }

        .prediction-content {
          display: flex;
          flex-direction: column;
          gap: 20px;
        }

        .prediction-main {
          display: flex;
          flex-direction: column;
          gap: 15px;
        }

        .prediction-direction {
          display: flex;
          align-items: center;
          gap: 15px;
        }

        .direction-indicator {
          font-size: 2rem;
        }

        .direction-text {
          font-size: 1.5rem;
          font-weight: bold;
        }

        .confidence-meter {
          display: flex;
          flex-direction: column;
          gap: 8px;
        }

        .confidence-bar {
          height: 8px;
          background: rgba(255, 255, 255, 0.2);
          border-radius: 4px;
          overflow: hidden;
        }

        .confidence-fill {
          height: 100%;
          background: linear-gradient(90deg, #4ade80, #22c55e);
          transition: width 0.3s ease;
        }

        .confidence-value {
          font-weight: bold;
          text-align: right;
        }

        .prediction-details {
          display: flex;
          flex-direction: column;
          gap: 10px;
        }

        .detail-item {
          display: flex;
          justify-content: space-between;
          padding: 8px 0;
          border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .performance-grid {
          display: flex;
          flex-direction: column;
          gap: 20px;
        }

        .performance-item {
          display: flex;
          flex-direction: column;
          gap: 8px;
        }

        .performance-label {
          font-size: 0.9rem;
          opacity: 0.8;
        }

        .performance-value {
          font-size: 1.5rem;
          font-weight: bold;
        }

        .performance-bar {
          height: 6px;
          background: rgba(255, 255, 255, 0.2);
          border-radius: 3px;
          overflow: hidden;
        }

        .performance-fill {
          height: 100%;
          background: linear-gradient(90deg, #3b82f6, #1d4ed8);
          transition: width 0.3s ease;
        }

        @media (max-width: 768px) {
          .analytics-content {
            grid-template-columns: 1fr;
          }
          
          .metrics-grid {
            grid-template-columns: 1fr;
          }
        }
      `}</style>
    </div>
  );
};

export default AnalyticsDashboardPage;
