# Kontour Coin Chainlink Integration Requirements
# Complete Chainlink mainnet integration with AI and RAG pipeline

# Core Python packages
numpy>=1.24.0
pandas>=2.0.0
python-dotenv>=1.0.0

# Web3 and blockchain
web3>=6.15.0
eth-account>=0.10.0
eth-utils>=2.3.0

# FastAPI and web framework
fastapi>=0.104.0
uvicorn>=0.24.0
pydantic>=2.5.0
httpx>=0.25.0

# Data processing and storage
redis>=5.0.0
kafka-python>=2.0.2
aiofiles>=23.2.0

# AI and machine learning
openai>=1.3.0
transformers>=4.36.0
torch>=2.1.0
scikit-learn>=1.3.0
sentence-transformers>=2.2.0

# Data analysis
scipy>=1.10.0
matplotlib>=3.7.0
seaborn>=0.12.0

# Async and concurrency
asyncio-mqtt>=0.13.0
aioredis>=2.0.0

# Monitoring and logging
prometheus-client>=0.19.0
psutil>=5.9.0

# Utilities
click>=8.1.0
rich>=13.7.0
tqdm>=4.66.0
pyyaml>=6.0.0

# Development and testing
pytest>=7.4.0
pytest-asyncio>=0.21.0
black>=23.11.0
flake8>=6.1.0

# Chainlink specific (if available)
# chainlink-feeds>=1.0.0  # Uncomment when available
