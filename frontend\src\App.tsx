import React, { useState, useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { ThemeProvider } from '@mui/material/styles';
import CssBaseline from '@mui/material/CssBaseline';
import { lightTheme, darkTheme } from './theme/professionalTheme';
import ProfessionalLayout from './components/Layout/ProfessionalLayout';
import ProfessionalDashboard from './components/Dashboard/ProfessionalDashboard';
import ProfessionalTradingInterface from './components/Trading/ProfessionalTradingInterface';
import WalletInterface from './components/Wallet/WalletInterface';
import AIAgentChat from './components/AIAgent/AIAgentChat';
import AIAgentDashboard from './components/AIAgent/AIAgentDashboard';
import WorkflowDashboard from './components/Workflow/WorkflowDashboard';
import WorkflowBuilder from './components/Workflow/WorkflowBuilder';
import { Web3Provider } from './contexts/Web3Context';
import { KontourProvider } from './contexts/KontourContext';
import { IntegrationProvider } from './contexts/IntegrationContext';
import { RealtimeProvider } from './contexts/RealtimeContext';

const App: React.FC = () => {
  const [darkMode, setDarkMode] = useState(() => {
    const saved = localStorage.getItem('darkMode');
    return saved ? JSON.parse(saved) : false;
  });

  useEffect(() => {
    localStorage.setItem('darkMode', JSON.stringify(darkMode));
  }, [darkMode]);

  const handleToggleDarkMode = () => {
    setDarkMode(!darkMode);
  };

  return (
    <Web3Provider>
      <KontourProvider>
        <IntegrationProvider>
          <RealtimeProvider>
            <ThemeProvider theme={darkMode ? darkTheme : lightTheme}>
              <CssBaseline />
              <Router>
                <ProfessionalLayout darkMode={darkMode} onToggleDarkMode={handleToggleDarkMode}>
                  <Routes>
                    <Route path="/" element={<ProfessionalDashboard />} />
                    <Route path="/dashboard" element={<ProfessionalDashboard />} />
                    <Route path="/trading" element={<ProfessionalTradingInterface />} />
                    <Route path="/wallet" element={<WalletInterface />} />
                    <Route path="/ai-agents" element={<AIAgentChat />} />
                    <Route path="/ai-dashboard" element={<AIAgentDashboard />} />
                    <Route path="/workflows/dashboard" element={<WorkflowDashboard />} />
                    <Route path="/workflows/builder" element={<WorkflowBuilder />} />
                    <Route path="/workflows" element={<WorkflowDashboard />} />
                    <Route path="/quantum" element={<ProfessionalDashboard />} />
                    <Route path="/analytics" element={<ProfessionalDashboard />} />
                    <Route path="/compliance" element={<ProfessionalDashboard />} />
                    <Route path="/genomics" element={<ProfessionalDashboard />} />
                    <Route path="/security" element={<ProfessionalDashboard />} />
                    <Route path="/reports" element={<ProfessionalDashboard />} />
                    <Route path="/settings" element={<ProfessionalDashboard />} />
                  </Routes>
                </ProfessionalLayout>
              </Router>
            </ThemeProvider>
          </RealtimeProvider>
        </IntegrationProvider>
      </KontourProvider>
    </Web3Provider>
  );
};

export default App;
