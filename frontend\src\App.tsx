import React from 'react';
import { BrowserRouter as Router, Routes, Route, Link } from 'react-router-dom';
import WorkflowPage from './pages/workflow';
import { Web3Provider } from './contexts/Web3Context';
import { KontourProvider } from './contexts/KontourContext';
import { IntegrationProvider } from './contexts/IntegrationContext';
import { RealtimeProvider } from './contexts/RealtimeContext';
import HomePage from './pages/home';
import BlockExplorerPage from './pages/block-explorer';
import WalletPage from './pages/wallet';
import AILabPage from './pages/ai-lab';
import EnhancedAILabPage from './pages/enhanced-ai-lab';
import QuantumDashboardPage from './pages/quantum-dashboard';
import IoTDashboardPage from './pages/iot-dashboard'; // New IoT Dashboard
import TradingDashboardPage from './pages/trading-dashboard';
import AnalyticsDashboardPage from './pages/analytics-dashboard';
import QuantumWorkflowPage from './pages/quantum-workflow';
import './styles/main.css';

const App: React.FC = () => {
  return (
    <Web3Provider>
      <KontourProvider>
        <IntegrationProvider>
          <RealtimeProvider>
            <Router>
              <div className="app">
                <header className="app-header">
                  <div className="container">
                    <div className="app-logo">
                      <img src="/images/kontour-logo.svg" alt="Kontour Coin Logo" />
                      <h1>Kontour Coin</h1>
                    </div>
                    <nav className="app-nav">
                      <ul>
                        <li>
                          <Link to="/">Home</Link>
                        </li>
                        <li>
                          <Link to="/workflow">Workflow</Link>
                        </li>
                        <li>
                          <Link to="/explorer">Block Explorer</Link>
                        </li>
                        <li>
                          <Link to="/wallet">Wallet</Link>
                        </li>
                        <li>
                          <Link to="/ai-lab">AI Lab</Link>
                        </li>
                        <li>
                          <Link to="/quantum">Quantum Dashboard</Link>
                        </li>
                        <li>
                          <Link to="/iot">IoT Dashboard</Link>
                        </li>
                        <li>
                          <Link to="/trading">Trading</Link>
                        </li>
                        <li>
                          <Link to="/analytics">Analytics</Link>
                        </li>
                        <li>
                          <Link to="/quantum-workflow">Quantum</Link>
                        </li>
                      </ul>
                    </nav>
                  </div>
                </header>

                <main className="main-content">
                  <Routes>
                    <Route path="/" element={<HomePage />} />
                    <Route path="/workflow" element={<WorkflowPage />} />
                    <Route path="/explorer" element={<BlockExplorerPage />} />
                    <Route path="/wallet" element={<WalletPage />} />
                    <Route path="/ai-lab" element={<EnhancedAILabPage />} />
                    <Route path="/quantum" element={<QuantumDashboardPage />} />
                    <Route path="/iot" element={<IoTDashboardPage />} />
                    <Route path="/trading" element={<TradingDashboardPage />} />
                    <Route path="/analytics" element={<AnalyticsDashboardPage />} />
                    <Route path="/quantum-workflow" element={<QuantumWorkflowPage />} />
                  </Routes>
                </main>
              </div>
            </Router>
          </RealtimeProvider>
        </IntegrationProvider>
      </KontourProvider>
    </Web3Provider>
  );
};

export default App;
