import React, { useState, useEffect, useCallback } from 'react';
import { useKontour } from '../contexts/KontourContext';
import { useRealtime } from '../contexts/RealtimeContext';

interface QuantumAlgorithm {
  name: string;
  class: string;
  qubits: number;
  resources: {
    qubits: number;
    depth: number;
    gates: number;
    estimated_time: number;
    memory_classical: number;
  };
}

interface QuantumWorkflowResult {
  algorithm_name: string;
  results: any;
  optimization: any;
  metrics: {
    execution_time: number;
    memory_usage: number;
    quantum_gates: number;
    accuracy: number;
    quantum_volume: number;
    circuit_depth: number;
    noise_resilience: number;
  };
  timestamp: string;
  workflow_id: string;
  ai_confidence: number;
}

interface MetricsReport {
  total_runs: number;
  avg_execution_time: number;
  avg_accuracy: number;
  total_quantum_gates: number;
  avg_quantum_volume: number;
  platform_connected: boolean;
}

const QuantumWorkflowDashboard: React.FC = () => {
  const { kontourBalance, kontourName } = useKontour();
  const { isConnected } = useRealtime();
  
  const [algorithms, setAlgorithms] = useState<Record<string, QuantumAlgorithm>>({});
  const [workflowResults, setWorkflowResults] = useState<QuantumWorkflowResult[]>([]);
  const [metricsReport, setMetricsReport] = useState<MetricsReport | null>(null);
  const [isExecuting, setIsExecuting] = useState<Record<string, boolean>>({});
  const [selectedAlgorithm, setSelectedAlgorithm] = useState<string>('');
  const [executionLog, setExecutionLog] = useState<string[]>([]);

  // Predefined quantum workflow configurations
  const workflowConfigs = {
    market_anomaly_detector: {
      name: 'Market Anomaly Detection',
      description: 'Quantum autoencoder for cryptocurrency market anomaly detection',
      icon: '📈',
      color: '#4ade80',
      params: { qubits: 8, window: 128, market_mode: true }
    },
    blockchain_security_analyzer: {
      name: 'Blockchain Security Analysis',
      description: 'Quantum stopping power for blockchain security assessment',
      icon: '🔒',
      color: '#3b82f6',
      params: { security_level: 256, quantum_resistance: 1.8 }
    },
    high_security_analyzer: {
      name: 'High Security Analysis',
      description: 'Advanced quantum security analysis for critical operations',
      icon: '🛡️',
      color: '#8b5cf6',
      params: { security_level: 512, quantum_resistance: 2.5 }
    },
    general_anomaly_detector: {
      name: 'General Anomaly Detection',
      description: 'General-purpose quantum anomaly detection',
      icon: '🔍',
      color: '#f59e0b',
      params: { qubits: 6, window: 64, market_mode: false }
    }
  };

  const fetchAlgorithms = useCallback(async () => {
    try {
      const response = await fetch('/api/quantum-workflow/algorithms');
      if (response.ok) {
        const data = await response.json();
        setAlgorithms(data);
      }
    } catch (error) {
      console.error('Error fetching algorithms:', error);
      addToLog('❌ Failed to fetch quantum algorithms');
    }
  }, []);

  const fetchMetricsReport = useCallback(async () => {
    try {
      const response = await fetch('/api/quantum-workflow/report');
      if (response.ok) {
        const data = await response.json();
        setMetricsReport(data);
      }
    } catch (error) {
      console.error('Error fetching metrics report:', error);
    }
  }, []);

  const addToLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString();
    setExecutionLog(prev => [`[${timestamp}] ${message}`, ...prev.slice(0, 49)]);
  };

  const executeQuantumWorkflow = async (algorithmName: string) => {
    if (isExecuting[algorithmName]) return;

    setIsExecuting(prev => ({ ...prev, [algorithmName]: true }));
    addToLog(`🚀 Starting ${algorithmName} execution...`);

    try {
      const workflowId = `${algorithmName}_${Date.now()}`;
      
      const response = await fetch('/api/quantum-workflow/execute', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          algorithm_name: algorithmName,
          optimize: true,
          workflow_id: workflowId
        })
      });

      if (response.ok) {
        const result = await response.json();
        setWorkflowResults(prev => [result, ...prev.slice(0, 9)]); // Keep last 10 results
        
        addToLog(`✅ ${algorithmName} completed successfully`);
        addToLog(`📊 Accuracy: ${(result.metrics.accuracy * 100).toFixed(1)}%`);
        addToLog(`⚡ Execution time: ${result.metrics.execution_time.toFixed(2)}ms`);
        addToLog(`🔬 Quantum gates: ${result.metrics.quantum_gates}`);
        
        // Fetch updated metrics
        await fetchMetricsReport();
      } else {
        throw new Error(`HTTP ${response.status}`);
      }
    } catch (error) {
      console.error('Error executing workflow:', error);
      addToLog(`❌ ${algorithmName} execution failed: ${error}`);
    } finally {
      setIsExecuting(prev => ({ ...prev, [algorithmName]: false }));
    }
  };

  const runQuantumDemo = async () => {
    addToLog('🎯 Starting comprehensive quantum workflow demo...');
    
    try {
      const response = await fetch('/api/quantum-workflow/demo', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }
      });

      if (response.ok) {
        const result = await response.json();
        addToLog(`🎉 Demo completed: ${result.executions} executions`);
        addToLog(`📈 Average accuracy: ${(result.avg_accuracy * 100).toFixed(1)}%`);
        addToLog(`⚡ Total quantum gates: ${result.total_gates.toLocaleString()}`);
        
        // Refresh data
        await Promise.all([fetchAlgorithms(), fetchMetricsReport()]);
      } else {
        throw new Error(`HTTP ${response.status}`);
      }
    } catch (error) {
      console.error('Error running demo:', error);
      addToLog(`❌ Demo execution failed: ${error}`);
    }
  };

  useEffect(() => {
    fetchAlgorithms();
    fetchMetricsReport();
    
    // Refresh data every 30 seconds
    const interval = setInterval(() => {
      fetchMetricsReport();
    }, 30000);
    
    return () => clearInterval(interval);
  }, [fetchAlgorithms, fetchMetricsReport]);

  const getStatusColor = (isRunning: boolean) => {
    return isRunning ? '#fbbf24' : '#4ade80';
  };

  const formatNumber = (num: number) => {
    if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`;
    if (num >= 1000) return `${(num / 1000).toFixed(1)}K`;
    return num.toString();
  };

  return (
    <div className="quantum-workflow-dashboard">
      <div className="dashboard-header">
        <div className="header-content">
          <h1>⚛️ Kontour Quantum Workflow Dashboard</h1>
          <div className="connection-status">
            <div className={`status-indicator ${isConnected ? 'connected' : 'disconnected'}`}>
              {isConnected ? '🟢' : '🔴'} {isConnected ? 'Connected' : 'Disconnected'}
            </div>
            <div className="kontour-info">
              💎 {kontourName} | Balance: {kontourBalance}
            </div>
          </div>
        </div>
      </div>

      {metricsReport && (
        <div className="metrics-overview">
          <div className="metric-card">
            <div className="metric-icon">🚀</div>
            <div className="metric-content">
              <div className="metric-value">{metricsReport.total_runs}</div>
              <div className="metric-label">Total Executions</div>
            </div>
          </div>
          
          <div className="metric-card">
            <div className="metric-icon">🎯</div>
            <div className="metric-content">
              <div className="metric-value">{(metricsReport.avg_accuracy * 100).toFixed(1)}%</div>
              <div className="metric-label">Avg Accuracy</div>
            </div>
          </div>
          
          <div className="metric-card">
            <div className="metric-icon">⚡</div>
            <div className="metric-content">
              <div className="metric-value">{metricsReport.avg_execution_time.toFixed(0)}ms</div>
              <div className="metric-label">Avg Time</div>
            </div>
          </div>
          
          <div className="metric-card">
            <div className="metric-icon">🔬</div>
            <div className="metric-content">
              <div className="metric-value">{formatNumber(metricsReport.total_quantum_gates)}</div>
              <div className="metric-label">Quantum Gates</div>
            </div>
          </div>
          
          <div className="metric-card">
            <div className="metric-icon">📊</div>
            <div className="metric-content">
              <div className="metric-value">{formatNumber(metricsReport.avg_quantum_volume)}</div>
              <div className="metric-label">Quantum Volume</div>
            </div>
          </div>
        </div>
      )}

      <div className="main-content">
        <div className="algorithms-section">
          <div className="section-header">
            <h2>🔬 Quantum Algorithms</h2>
            <button 
              className="demo-button"
              onClick={runQuantumDemo}
              disabled={Object.values(isExecuting).some(Boolean)}
            >
              🎯 Run Complete Demo
            </button>
          </div>
          
          <div className="algorithms-grid">
            {Object.entries(workflowConfigs).map(([key, config]) => {
              const algorithm = algorithms[key];
              const isRunning = isExecuting[key];
              
              return (
                <div key={key} className="algorithm-card">
                  <div className="algorithm-header">
                    <div className="algorithm-icon" style={{ color: config.color }}>
                      {config.icon}
                    </div>
                    <div className="algorithm-info">
                      <h3>{config.name}</h3>
                      <p>{config.description}</p>
                    </div>
                  </div>
                  
                  {algorithm && (
                    <div className="algorithm-specs">
                      <div className="spec-item">
                        <span>Qubits:</span>
                        <span>{algorithm.qubits}</span>
                      </div>
                      <div className="spec-item">
                        <span>Gates:</span>
                        <span>{algorithm.resources.gates}</span>
                      </div>
                      <div className="spec-item">
                        <span>Depth:</span>
                        <span>{algorithm.resources.depth}</span>
                      </div>
                      <div className="spec-item">
                        <span>Est. Time:</span>
                        <span>{algorithm.resources.estimated_time.toFixed(1)}μs</span>
                      </div>
                    </div>
                  )}
                  
                  <button
                    className={`execute-button ${isRunning ? 'running' : ''}`}
                    onClick={() => executeQuantumWorkflow(key)}
                    disabled={isRunning}
                    style={{ backgroundColor: isRunning ? '#fbbf24' : config.color }}
                  >
                    {isRunning ? '⏳ Executing...' : '🚀 Execute'}
                  </button>
                </div>
              );
            })}
          </div>
        </div>

        <div className="results-section">
          <div className="section-header">
            <h2>📊 Recent Results</h2>
          </div>
          
          <div className="results-list">
            {workflowResults.length === 0 ? (
              <div className="no-results">
                <div className="no-results-icon">🔬</div>
                <p>No quantum workflows executed yet</p>
                <p>Click "Execute" on any algorithm to see results</p>
              </div>
            ) : (
              workflowResults.map((result, index) => (
                <div key={`${result.workflow_id}-${index}`} className="result-card">
                  <div className="result-header">
                    <div className="result-title">
                      {workflowConfigs[result.algorithm_name as keyof typeof workflowConfigs]?.icon || '⚛️'} 
                      {workflowConfigs[result.algorithm_name as keyof typeof workflowConfigs]?.name || result.algorithm_name}
                    </div>
                    <div className="result-timestamp">
                      {new Date(result.timestamp).toLocaleTimeString()}
                    </div>
                  </div>
                  
                  <div className="result-metrics">
                    <div className="result-metric">
                      <span>Accuracy:</span>
                      <span className="metric-value">{(result.metrics.accuracy * 100).toFixed(1)}%</span>
                    </div>
                    <div className="result-metric">
                      <span>Time:</span>
                      <span className="metric-value">{result.metrics.execution_time.toFixed(2)}ms</span>
                    </div>
                    <div className="result-metric">
                      <span>Gates:</span>
                      <span className="metric-value">{result.metrics.quantum_gates}</span>
                    </div>
                    <div className="result-metric">
                      <span>AI Confidence:</span>
                      <span className="metric-value">{(result.ai_confidence * 100).toFixed(1)}%</span>
                    </div>
                  </div>
                  
                  {result.results.anomaly_count !== undefined && (
                    <div className="result-details">
                      <span>Anomalies Detected: {result.results.anomaly_count}</span>
                    </div>
                  )}
                  
                  {result.results.security_analysis && (
                    <div className="result-details">
                      <span>Security Level: {result.results.security_analysis.security_level}</span>
                      <span>Security Score: {(result.results.security_analysis.overall_security_score * 100).toFixed(1)}%</span>
                    </div>
                  )}
                </div>
              ))
            )}
          </div>
        </div>

        <div className="execution-log">
          <div className="section-header">
            <h2>📝 Execution Log</h2>
            <button 
              className="clear-log-button"
              onClick={() => setExecutionLog([])}
            >
              🗑️ Clear
            </button>
          </div>
          
          <div className="log-content">
            {executionLog.length === 0 ? (
              <div className="no-logs">No execution logs yet</div>
            ) : (
              executionLog.map((log, index) => (
                <div key={index} className="log-entry">
                  {log}
                </div>
              ))
            )}
          </div>
        </div>
      </div>

      <style jsx>{`
        .quantum-workflow-dashboard {
          padding: 20px;
          background: linear-gradient(135deg, #1e1b4b 0%, #312e81 50%, #581c87 100%);
          min-height: 100vh;
          color: white;
        }

        .dashboard-header {
          margin-bottom: 30px;
        }

        .header-content {
          display: flex;
          justify-content: space-between;
          align-items: center;
        }

        .header-content h1 {
          font-size: 2.5rem;
          margin: 0;
          text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .connection-status {
          display: flex;
          flex-direction: column;
          align-items: flex-end;
          gap: 8px;
        }

        .status-indicator {
          padding: 8px 16px;
          border-radius: 20px;
          background: rgba(255, 255, 255, 0.1);
          backdrop-filter: blur(10px);
        }

        .kontour-info {
          font-size: 1.1rem;
          font-weight: bold;
        }

        .metrics-overview {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
          gap: 20px;
          margin-bottom: 30px;
        }

        .metric-card {
          display: flex;
          align-items: center;
          gap: 15px;
          padding: 20px;
          background: rgba(255, 255, 255, 0.1);
          border-radius: 12px;
          backdrop-filter: blur(10px);
          border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .metric-icon {
          font-size: 2rem;
        }

        .metric-value {
          font-size: 1.8rem;
          font-weight: bold;
          margin-bottom: 4px;
        }

        .metric-label {
          font-size: 0.9rem;
          opacity: 0.8;
        }

        .main-content {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 30px;
        }

        .section-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 20px;
        }

        .section-header h2 {
          margin: 0;
          font-size: 1.5rem;
        }

        .demo-button, .clear-log-button {
          padding: 10px 20px;
          background: rgba(255, 255, 255, 0.2);
          border: none;
          border-radius: 8px;
          color: white;
          cursor: pointer;
          font-weight: bold;
          transition: all 0.3s ease;
        }

        .demo-button:hover, .clear-log-button:hover {
          background: rgba(255, 255, 255, 0.3);
          transform: translateY(-2px);
        }

        .demo-button:disabled {
          opacity: 0.5;
          cursor: not-allowed;
          transform: none;
        }

        .algorithms-grid {
          display: grid;
          gap: 20px;
        }

        .algorithm-card {
          padding: 20px;
          background: rgba(255, 255, 255, 0.1);
          border-radius: 12px;
          backdrop-filter: blur(10px);
          border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .algorithm-header {
          display: flex;
          align-items: flex-start;
          gap: 15px;
          margin-bottom: 15px;
        }

        .algorithm-icon {
          font-size: 2rem;
        }

        .algorithm-info h3 {
          margin: 0 0 8px 0;
          font-size: 1.2rem;
        }

        .algorithm-info p {
          margin: 0;
          opacity: 0.8;
          font-size: 0.9rem;
        }

        .algorithm-specs {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 8px;
          margin-bottom: 15px;
        }

        .spec-item {
          display: flex;
          justify-content: space-between;
          padding: 4px 0;
          font-size: 0.9rem;
        }

        .execute-button {
          width: 100%;
          padding: 12px;
          border: none;
          border-radius: 8px;
          color: white;
          font-weight: bold;
          cursor: pointer;
          transition: all 0.3s ease;
        }

        .execute-button:hover:not(:disabled) {
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        }

        .execute-button:disabled {
          cursor: not-allowed;
        }

        .results-list {
          max-height: 600px;
          overflow-y: auto;
        }

        .no-results {
          text-align: center;
          padding: 40px;
          opacity: 0.6;
        }

        .no-results-icon {
          font-size: 3rem;
          margin-bottom: 15px;
        }

        .result-card {
          padding: 15px;
          margin-bottom: 15px;
          background: rgba(255, 255, 255, 0.1);
          border-radius: 8px;
          backdrop-filter: blur(10px);
        }

        .result-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 10px;
        }

        .result-title {
          font-weight: bold;
          font-size: 1.1rem;
        }

        .result-timestamp {
          font-size: 0.9rem;
          opacity: 0.7;
        }

        .result-metrics {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 8px;
          margin-bottom: 10px;
        }

        .result-metric {
          display: flex;
          justify-content: space-between;
          font-size: 0.9rem;
        }

        .result-metric .metric-value {
          font-weight: bold;
          color: #4ade80;
        }

        .result-details {
          display: flex;
          gap: 15px;
          font-size: 0.9rem;
          opacity: 0.8;
        }

        .execution-log {
          grid-column: 1 / -1;
        }

        .log-content {
          max-height: 300px;
          overflow-y: auto;
          background: rgba(0, 0, 0, 0.3);
          border-radius: 8px;
          padding: 15px;
        }

        .no-logs {
          text-align: center;
          opacity: 0.6;
          padding: 20px;
        }

        .log-entry {
          font-family: monospace;
          font-size: 0.9rem;
          margin-bottom: 4px;
          padding: 2px 0;
        }

        @media (max-width: 768px) {
          .main-content {
            grid-template-columns: 1fr;
          }
          
          .metrics-overview {
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
          }
          
          .header-content {
            flex-direction: column;
            gap: 15px;
          }
        }
      `}</style>
    </div>
  );
};

export default QuantumWorkflowDashboard;
