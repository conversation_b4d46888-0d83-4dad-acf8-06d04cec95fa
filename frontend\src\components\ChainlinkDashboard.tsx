import React, { useState, useEffect, useCallback } from 'react';
import { useKontour } from '../contexts/KontourContext';
import { useRealtime } from '../contexts/RealtimeContext';

interface MarketData {
  eth_price: number;
  btc_price: number;
  link_price: number;
  timestamp: string;
  volatility: number;
  sentiment: string;
  volume_24h: number;
  market_cap: number;
}

interface AIDecision {
  decision_id: string;
  decision_type: string;
  confidence: number;
  asset: string;
  action: string;
  target_price: number;
  timestamp: string;
  executed: boolean;
  reasoning: string;
}

interface TradingSignal {
  signal_id: string;
  asset: string;
  action: string;
  confidence: number;
  target_price: number;
  stop_loss: number;
  take_profit: number;
  timestamp: string;
  source: string;
}

interface RAGQueryResult {
  query: string;
  response: {
    summary: string;
    full_response: string;
    confidence: number;
    trading_recommendation?: any;
  };
  timestamp: string;
  confidence: number;
}

const ChainlinkDashboard: React.FC = () => {
  const { kontourBalance, kontourName } = useKontour();
  const { isConnected } = useRealtime();
  
  const [marketData, setMarketData] = useState<MarketData | null>(null);
  const [aiDecisions, setAIDecisions] = useState<AIDecision[]>([]);
  const [tradingSignals, setTradingSignals] = useState<TradingSignal[]>([]);
  const [ragResults, setRAGResults] = useState<RAGQueryResult[]>([]);
  const [isLoading, setIsLoading] = useState<Record<string, boolean>>({});
  const [ragQuery, setRAGQuery] = useState('');
  const [executionLog, setExecutionLog] = useState<string[]>([]);

  const chainlinkServices = {
    'Data Feeds': {
      icon: '📊',
      description: 'Real-time price data from decentralized oracles',
      color: '#4ade80',
      status: 'active'
    },
    'VRF v2.5': {
      icon: '🎲',
      description: 'Verifiable random numbers for fair AI decisions',
      color: '#3b82f6',
      status: 'active'
    },
    'Functions': {
      icon: '🔗',
      description: 'Connect to any API for RAG pipeline integration',
      color: '#8b5cf6',
      status: 'active'
    },
    'Automation': {
      icon: '⚙️',
      description: 'Automated execution of trading strategies',
      color: '#f59e0b',
      status: 'active'
    },
    'CCIP': {
      icon: '🌐',
      description: 'Cross-chain interoperability protocol',
      color: '#ef4444',
      status: 'active'
    }
  };

  const addToLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString();
    setExecutionLog(prev => [`[${timestamp}] ${message}`, ...prev.slice(0, 49)]);
  };

  const fetchMarketData = useCallback(async () => {
    try {
      const response = await fetch('/api/chainlink/market-data');
      if (response.ok) {
        const data = await response.json();
        setMarketData(data);
        addToLog(`📊 Market data updated: ETH $${data.eth_price.toFixed(2)}`);
      }
    } catch (error) {
      console.error('Error fetching market data:', error);
      addToLog('❌ Failed to fetch market data');
    }
  }, []);

  const fetchAIDecisions = useCallback(async () => {
    try {
      const response = await fetch('/api/chainlink/ai-decisions');
      if (response.ok) {
        const data = await response.json();
        setAIDecisions(data);
      }
    } catch (error) {
      console.error('Error fetching AI decisions:', error);
    }
  }, []);

  const fetchTradingSignals = useCallback(async () => {
    try {
      const response = await fetch('/api/chainlink/trading-signals');
      if (response.ok) {
        const data = await response.json();
        setTradingSignals(data);
      }
    } catch (error) {
      console.error('Error fetching trading signals:', error);
    }
  }, []);

  const requestVRF = async () => {
    setIsLoading(prev => ({ ...prev, vrf: true }));
    addToLog('🎲 Requesting random number from Chainlink VRF...');

    try {
      const response = await fetch('/api/chainlink/vrf/request', {
        method: 'POST'
      });

      if (response.ok) {
        const result = await response.json();
        addToLog(`✅ VRF request submitted: ${result.request_id}`);
        addToLog('⏳ Waiting for VRF response and AI decision generation...');
        
        // Refresh AI decisions after a delay
        setTimeout(() => {
          fetchAIDecisions();
          addToLog('🤖 AI decision generated using VRF randomness');
        }, 3000);
      } else {
        throw new Error(`HTTP ${response.status}`);
      }
    } catch (error) {
      console.error('VRF request failed:', error);
      addToLog(`❌ VRF request failed: ${error}`);
    } finally {
      setIsLoading(prev => ({ ...prev, vrf: false }));
    }
  };

  const executeRAGQuery = async () => {
    if (!ragQuery.trim()) return;

    setIsLoading(prev => ({ ...prev, rag: true }));
    addToLog(`🔗 Executing RAG query: "${ragQuery}"`);

    try {
      const response = await fetch('/api/chainlink/rag/query', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ query: ragQuery })
      });

      if (response.ok) {
        const result = await response.json();
        setRAGResults(prev => [result, ...prev.slice(0, 4)]); // Keep last 5 results
        addToLog(`✅ RAG query completed with ${(result.confidence * 100).toFixed(1)}% confidence`);
        
        if (result.response.trading_recommendation) {
          addToLog('📈 Trading recommendation generated from AI analysis');
          setTimeout(fetchTradingSignals, 1000);
        }
        
        setRAGQuery('');
      } else {
        throw new Error(`HTTP ${response.status}`);
      }
    } catch (error) {
      console.error('RAG query failed:', error);
      addToLog(`❌ RAG query failed: ${error}`);
    } finally {
      setIsLoading(prev => ({ ...prev, rag: false }));
    }
  };

  const performAutomation = async () => {
    setIsLoading(prev => ({ ...prev, automation: true }));
    addToLog('⚙️ Performing automated upkeep...');

    try {
      const response = await fetch('/api/chainlink/automation/upkeep', {
        method: 'POST'
      });

      if (response.ok) {
        const result = await response.json();
        addToLog('✅ Automation upkeep completed successfully');
        addToLog('📊 Market data updated, portfolio rebalanced');
        
        // Refresh all data
        await Promise.all([
          fetchMarketData(),
          fetchAIDecisions(),
          fetchTradingSignals()
        ]);
      } else {
        throw new Error(`HTTP ${response.status}`);
      }
    } catch (error) {
      console.error('Automation failed:', error);
      addToLog(`❌ Automation failed: ${error}`);
    } finally {
      setIsLoading(prev => ({ ...prev, automation: false }));
    }
  };

  useEffect(() => {
    // Initial data fetch
    fetchMarketData();
    fetchAIDecisions();
    fetchTradingSignals();

    // Set up periodic updates
    const interval = setInterval(() => {
      fetchMarketData();
    }, 30000); // Update every 30 seconds

    return () => clearInterval(interval);
  }, [fetchMarketData, fetchAIDecisions, fetchTradingSignals]);

  const getSentimentColor = (sentiment: string) => {
    switch (sentiment) {
      case 'BULLISH': return '#4ade80';
      case 'BEARISH': return '#ef4444';
      default: return '#6b7280';
    }
  };

  const getActionColor = (action: string) => {
    switch (action) {
      case 'BUY': return '#4ade80';
      case 'SELL': return '#ef4444';
      default: return '#6b7280';
    }
  };

  return (
    <div className="chainlink-dashboard">
      <div className="dashboard-header">
        <div className="header-content">
          <h1>🔗 Kontour Chainlink Integration</h1>
          <div className="connection-status">
            <div className={`status-indicator ${isConnected ? 'connected' : 'disconnected'}`}>
              {isConnected ? '🟢' : '🔴'} {isConnected ? 'Connected' : 'Disconnected'}
            </div>
            <div className="kontour-info">
              💎 {kontourName} | Balance: {kontourBalance}
            </div>
          </div>
        </div>
      </div>

      {/* Chainlink Services Overview */}
      <div className="services-overview">
        <h2>🛠️ Chainlink Services</h2>
        <div className="services-grid">
          {Object.entries(chainlinkServices).map(([name, service]) => (
            <div key={name} className="service-card">
              <div className="service-header">
                <div className="service-icon" style={{ color: service.color }}>
                  {service.icon}
                </div>
                <div className="service-info">
                  <h3>{name}</h3>
                  <p>{service.description}</p>
                </div>
              </div>
              <div className={`service-status ${service.status}`}>
                {service.status === 'active' ? '✅ Active' : '⏸️ Inactive'}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Market Data Section */}
      {marketData && (
        <div className="market-data-section">
          <h2>📊 Real-time Market Data</h2>
          <div className="market-data-grid">
            <div className="price-card">
              <div className="price-header">
                <span className="asset-icon">🔷</span>
                <span className="asset-name">ETH/USD</span>
              </div>
              <div className="price-value">${marketData.eth_price.toFixed(2)}</div>
            </div>
            
            <div className="price-card">
              <div className="price-header">
                <span className="asset-icon">🟠</span>
                <span className="asset-name">BTC/USD</span>
              </div>
              <div className="price-value">${marketData.btc_price.toFixed(2)}</div>
            </div>
            
            <div className="price-card">
              <div className="price-header">
                <span className="asset-icon">🔗</span>
                <span className="asset-name">LINK/USD</span>
              </div>
              <div className="price-value">${marketData.link_price.toFixed(2)}</div>
            </div>
            
            <div className="metric-card">
              <div className="metric-label">Volatility</div>
              <div className="metric-value">{marketData.volatility.toFixed(2)}%</div>
            </div>
            
            <div className="metric-card">
              <div className="metric-label">Sentiment</div>
              <div 
                className="metric-value" 
                style={{ color: getSentimentColor(marketData.sentiment) }}
              >
                {marketData.sentiment}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Interactive Controls */}
      <div className="controls-section">
        <h2>🎮 Interactive Controls</h2>
        <div className="controls-grid">
          <div className="control-card">
            <h3>🎲 Chainlink VRF</h3>
            <p>Request verifiable random number for AI decision making</p>
            <button
              className="control-button vrf"
              onClick={requestVRF}
              disabled={isLoading.vrf}
            >
              {isLoading.vrf ? '⏳ Requesting...' : '🎲 Request Random Number'}
            </button>
          </div>
          
          <div className="control-card">
            <h3>⚙️ Automation</h3>
            <p>Perform automated upkeep and portfolio rebalancing</p>
            <button
              className="control-button automation"
              onClick={performAutomation}
              disabled={isLoading.automation}
            >
              {isLoading.automation ? '⏳ Processing...' : '⚙️ Perform Upkeep'}
            </button>
          </div>
          
          <div className="control-card rag-card">
            <h3>🔗 RAG Pipeline</h3>
            <p>Query AI assistant with market context</p>
            <div className="rag-input-group">
              <input
                type="text"
                value={ragQuery}
                onChange={(e) => setRAGQuery(e.target.value)}
                placeholder="Ask about market conditions, trading strategies..."
                className="rag-input"
                onKeyPress={(e) => e.key === 'Enter' && executeRAGQuery()}
              />
              <button
                className="control-button rag"
                onClick={executeRAGQuery}
                disabled={isLoading.rag || !ragQuery.trim()}
              >
                {isLoading.rag ? '⏳ Processing...' : '🔗 Query AI'}
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Results Section */}
      <div className="results-section">
        <div className="results-grid">
          {/* AI Decisions */}
          <div className="result-panel">
            <h3>🤖 AI Decisions</h3>
            <div className="result-list">
              {aiDecisions.length === 0 ? (
                <div className="no-data">No AI decisions yet</div>
              ) : (
                aiDecisions.slice(0, 5).map((decision) => (
                  <div key={decision.decision_id} className="decision-item">
                    <div className="decision-header">
                      <span className="decision-type">{decision.decision_type}</span>
                      <span className="decision-confidence">{decision.confidence.toFixed(1)}%</span>
                    </div>
                    <div className="decision-details">
                      <span style={{ color: getActionColor(decision.action) }}>
                        {decision.action} {decision.asset}
                      </span>
                      <span className="target-price">
                        Target: ${decision.target_price.toFixed(2)}
                      </span>
                    </div>
                    <div className="decision-reasoning">
                      {decision.reasoning}
                    </div>
                  </div>
                ))
              )}
            </div>
          </div>

          {/* Trading Signals */}
          <div className="result-panel">
            <h3>📈 Trading Signals</h3>
            <div className="result-list">
              {tradingSignals.length === 0 ? (
                <div className="no-data">No trading signals yet</div>
              ) : (
                tradingSignals.slice(0, 5).map((signal) => (
                  <div key={signal.signal_id} className="signal-item">
                    <div className="signal-header">
                      <span style={{ color: getActionColor(signal.action) }}>
                        {signal.action} {signal.asset}
                      </span>
                      <span className="signal-confidence">{signal.confidence.toFixed(1)}%</span>
                    </div>
                    <div className="signal-details">
                      <span>Target: ${signal.target_price.toFixed(2)}</span>
                      <span>Source: {signal.source}</span>
                    </div>
                  </div>
                ))
              )}
            </div>
          </div>

          {/* RAG Results */}
          <div className="result-panel">
            <h3>🧠 RAG Query Results</h3>
            <div className="result-list">
              {ragResults.length === 0 ? (
                <div className="no-data">No RAG queries yet</div>
              ) : (
                ragResults.map((result, index) => (
                  <div key={index} className="rag-result">
                    <div className="rag-query">
                      <strong>Q:</strong> {result.query}
                    </div>
                    <div className="rag-response">
                      <strong>A:</strong> {result.response.summary}
                    </div>
                    <div className="rag-confidence">
                      Confidence: {(result.confidence * 100).toFixed(1)}%
                    </div>
                  </div>
                ))
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Execution Log */}
      <div className="execution-log">
        <div className="log-header">
          <h3>📝 Execution Log</h3>
          <button 
            className="clear-log-button"
            onClick={() => setExecutionLog([])}
          >
            🗑️ Clear
          </button>
        </div>
        <div className="log-content">
          {executionLog.length === 0 ? (
            <div className="no-logs">No execution logs yet</div>
          ) : (
            executionLog.map((log, index) => (
              <div key={index} className="log-entry">
                {log}
              </div>
            ))
          )}
        </div>
      </div>

      <style jsx>{`
        .chainlink-dashboard {
          padding: 20px;
          background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
          min-height: 100vh;
          color: white;
        }

        .dashboard-header {
          margin-bottom: 30px;
        }

        .header-content {
          display: flex;
          justify-content: space-between;
          align-items: center;
        }

        .header-content h1 {
          font-size: 2.5rem;
          margin: 0;
          text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .connection-status {
          display: flex;
          flex-direction: column;
          align-items: flex-end;
          gap: 8px;
        }

        .status-indicator {
          padding: 8px 16px;
          border-radius: 20px;
          background: rgba(255, 255, 255, 0.1);
          backdrop-filter: blur(10px);
        }

        .kontour-info {
          font-size: 1.1rem;
          font-weight: bold;
        }

        .services-overview {
          margin-bottom: 30px;
        }

        .services-overview h2 {
          margin-bottom: 20px;
          font-size: 1.5rem;
        }

        .services-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
          gap: 20px;
        }

        .service-card {
          padding: 20px;
          background: rgba(255, 255, 255, 0.1);
          border-radius: 12px;
          backdrop-filter: blur(10px);
          border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .service-header {
          display: flex;
          align-items: center;
          gap: 15px;
          margin-bottom: 15px;
        }

        .service-icon {
          font-size: 2rem;
        }

        .service-info h3 {
          margin: 0 0 5px 0;
          font-size: 1.2rem;
        }

        .service-info p {
          margin: 0;
          opacity: 0.8;
          font-size: 0.9rem;
        }

        .service-status {
          text-align: right;
          font-weight: bold;
        }

        .service-status.active {
          color: #4ade80;
        }

        .market-data-section {
          margin-bottom: 30px;
        }

        .market-data-section h2 {
          margin-bottom: 20px;
          font-size: 1.5rem;
        }

        .market-data-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
          gap: 20px;
        }

        .price-card, .metric-card {
          padding: 20px;
          background: rgba(255, 255, 255, 0.1);
          border-radius: 12px;
          backdrop-filter: blur(10px);
          text-align: center;
        }

        .price-header {
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 10px;
          margin-bottom: 10px;
        }

        .asset-icon {
          font-size: 1.5rem;
        }

        .asset-name {
          font-weight: bold;
        }

        .price-value {
          font-size: 2rem;
          font-weight: bold;
          color: #4ade80;
        }

        .metric-label {
          font-size: 0.9rem;
          opacity: 0.8;
          margin-bottom: 5px;
        }

        .metric-value {
          font-size: 1.5rem;
          font-weight: bold;
        }

        .controls-section {
          margin-bottom: 30px;
        }

        .controls-section h2 {
          margin-bottom: 20px;
          font-size: 1.5rem;
        }

        .controls-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
          gap: 20px;
        }

        .control-card {
          padding: 20px;
          background: rgba(255, 255, 255, 0.1);
          border-radius: 12px;
          backdrop-filter: blur(10px);
          border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .control-card h3 {
          margin: 0 0 10px 0;
          font-size: 1.2rem;
        }

        .control-card p {
          margin: 0 0 15px 0;
          opacity: 0.8;
          font-size: 0.9rem;
        }

        .control-button {
          width: 100%;
          padding: 12px;
          border: none;
          border-radius: 8px;
          color: white;
          font-weight: bold;
          cursor: pointer;
          transition: all 0.3s ease;
        }

        .control-button.vrf {
          background: #3b82f6;
        }

        .control-button.automation {
          background: #f59e0b;
        }

        .control-button.rag {
          background: #8b5cf6;
        }

        .control-button:hover:not(:disabled) {
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        }

        .control-button:disabled {
          opacity: 0.5;
          cursor: not-allowed;
          transform: none;
        }

        .rag-card {
          grid-column: 1 / -1;
        }

        .rag-input-group {
          display: flex;
          gap: 10px;
        }

        .rag-input {
          flex: 1;
          padding: 12px;
          border: 1px solid rgba(255, 255, 255, 0.2);
          border-radius: 8px;
          background: rgba(255, 255, 255, 0.1);
          color: white;
          font-size: 1rem;
        }

        .rag-input::placeholder {
          color: rgba(255, 255, 255, 0.5);
        }

        .results-section {
          margin-bottom: 30px;
        }

        .results-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
          gap: 20px;
        }

        .result-panel {
          background: rgba(255, 255, 255, 0.1);
          border-radius: 12px;
          backdrop-filter: blur(10px);
          border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .result-panel h3 {
          margin: 0;
          padding: 20px 20px 10px 20px;
          font-size: 1.2rem;
          border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .result-list {
          padding: 20px;
          max-height: 400px;
          overflow-y: auto;
        }

        .no-data {
          text-align: center;
          opacity: 0.6;
          padding: 20px;
        }

        .decision-item, .signal-item, .rag-result {
          padding: 15px;
          margin-bottom: 15px;
          background: rgba(255, 255, 255, 0.05);
          border-radius: 8px;
          border-left: 4px solid #4ade80;
        }

        .decision-header, .signal-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 8px;
        }

        .decision-type, .signal-confidence, .decision-confidence {
          font-weight: bold;
          font-size: 0.9rem;
        }

        .decision-details, .signal-details {
          display: flex;
          justify-content: space-between;
          margin-bottom: 8px;
          font-size: 0.9rem;
        }

        .decision-reasoning {
          font-size: 0.8rem;
          opacity: 0.8;
          font-style: italic;
        }

        .rag-query, .rag-response {
          margin-bottom: 8px;
          font-size: 0.9rem;
        }

        .rag-confidence {
          font-size: 0.8rem;
          opacity: 0.8;
          text-align: right;
        }

        .execution-log {
          background: rgba(255, 255, 255, 0.1);
          border-radius: 12px;
          backdrop-filter: blur(10px);
          border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .log-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 20px;
          border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .log-header h3 {
          margin: 0;
          font-size: 1.2rem;
        }

        .clear-log-button {
          padding: 8px 16px;
          background: rgba(255, 255, 255, 0.2);
          border: none;
          border-radius: 6px;
          color: white;
          cursor: pointer;
          font-size: 0.9rem;
        }

        .clear-log-button:hover {
          background: rgba(255, 255, 255, 0.3);
        }

        .log-content {
          padding: 20px;
          max-height: 300px;
          overflow-y: auto;
        }

        .no-logs {
          text-align: center;
          opacity: 0.6;
          padding: 20px;
        }

        .log-entry {
          font-family: monospace;
          font-size: 0.9rem;
          margin-bottom: 4px;
          padding: 2px 0;
        }

        @media (max-width: 768px) {
          .header-content {
            flex-direction: column;
            gap: 15px;
          }
          
          .services-grid, .controls-grid, .results-grid {
            grid-template-columns: 1fr;
          }
          
          .market-data-grid {
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
          }
        }
      `}</style>
    </div>
  );
};

export default ChainlinkDashboard;
