import React from 'react';
import UnifiedWorkflowManager from '../components/UnifiedWorkflowManager';

const WorkflowPage: React.FC = () => {
  return (
    <div className="workflow-page">
      <div className="page-header">
        <h1>💎 Kontour Coin Workflow Management</h1>
        <p>Comprehensive BPMN-style workflow orchestration for frontend, backend, blockchain, AI, and quantum subsystems</p>
      </div>
      
      <UnifiedWorkflowManager />
      
      <style>{`
        .workflow-page {
          min-height: 100vh;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .page-header {
          padding: 40px 20px 20px 20px;
          text-align: center;
          color: white;
        }
        
        .page-header h1 {
          font-size: 2.5rem;
          margin-bottom: 10px;
          text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }
        
        .page-header p {
          font-size: 1.2rem;
          opacity: 0.9;
          max-width: 800px;
          margin: 0 auto;
        }
      `}</style>
    </div>
  );
};

export default WorkflowPage;
