"""
Kontour Coin Quantum Workflow API Service
FastAPI service providing REST endpoints for quantum workflow management
Integrated with Kontour platform infrastructure
"""

import asyncio
import sys
import os
from pathlib import Path
from fastapi import FastAPI, HTTPException, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import Dict, Any, Optional, List
import logging
from datetime import datetime
import redis
from kafka import KafkaProducer
import json

# Add quantum-workflow to path
sys.path.append(str(Path(__file__).parent.parent / "quantum-workflow"))

from core import KontourQuantumWorkflowManager
from autoencoder import KontourQuantumAutoencoder
from stopping_power import KontourStoppingPowerAlgo, KontourSystemParams
from metrics import KontourQuantumMetricsCollector

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Pydantic models for API
class WorkflowExecutionRequest(BaseModel):
    algorithm_name: str
    optimize: bool = True
    workflow_id: Optional[str] = None
    parameters: Optional[Dict[str, Any]] = None

class AlgorithmRegistrationRequest(BaseModel):
    name: str
    algorithm_type: str
    parameters: Dict[str, Any]

class HealthResponse(BaseModel):
    status: str
    service: str
    timestamp: str
    algorithms_registered: int
    total_executions: int
    platform_connected: bool

# Initialize FastAPI app
app = FastAPI(
    title="Kontour Quantum Workflow API",
    description="REST API for Kontour Coin quantum workflow management",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Global instances
workflow_manager: Optional[KontourQuantumWorkflowManager] = None
metrics_collector: Optional[KontourQuantumMetricsCollector] = None

@app.on_event("startup")
async def startup_event():
    """Initialize quantum workflow components on startup"""
    global workflow_manager, metrics_collector
    
    logger.info("Initializing Kontour Quantum Workflow API...")
    
    try:
        # Initialize workflow manager
        workflow_manager = KontourQuantumWorkflowManager()
        
        # Initialize metrics collector
        try:
            redis_client = redis.Redis(host='localhost', port=6379, decode_responses=True)
            kafka_producer = KafkaProducer(
                bootstrap_servers=['localhost:9092'],
                value_serializer=lambda x: json.dumps(x).encode('utf-8')
            )
            metrics_collector = KontourQuantumMetricsCollector(redis_client, kafka_producer)
        except Exception as e:
            logger.warning(f"Platform integration failed: {e}. Using standalone metrics collector.")
            metrics_collector = KontourQuantumMetricsCollector()
        
        # Register default quantum algorithms
        await register_default_algorithms()
        
        logger.info("✅ Quantum Workflow API initialized successfully")
        
    except Exception as e:
        logger.error(f"❌ Failed to initialize Quantum Workflow API: {e}")
        raise

async def register_default_algorithms():
    """Register default quantum algorithms"""
    global workflow_manager
    
    if not workflow_manager:
        return
    
    try:
        # 1. Market Anomaly Detection Autoencoder
        market_autoencoder = KontourQuantumAutoencoder(
            qubits=8, 
            window=128, 
            market_mode=True
        )
        workflow_manager.register("market_anomaly_detector", market_autoencoder)
        
        # 2. General Anomaly Detection Autoencoder
        general_autoencoder = KontourQuantumAutoencoder(
            qubits=6, 
            window=64, 
            market_mode=False
        )
        workflow_manager.register("general_anomaly_detector", general_autoencoder)
        
        # 3. Blockchain Security Analyzer
        security_params = KontourSystemParams(
            projectile_mass=4.0,
            projectile_charge=2.0,
            target_density=1.2,
            electrons=12,
            temperature=300.0,
            blockchain_security_level=256,
            quantum_resistance_factor=1.8,
            cryptographic_strength=1.0,
            network_hash_rate=1e15,
            consensus_difficulty=1.2
        )
        
        security_algo = KontourStoppingPowerAlgo(security_params, security_mode=True)
        workflow_manager.register("blockchain_security_analyzer", security_algo)
        
        # 4. High Security Analyzer
        high_security_params = KontourSystemParams(
            projectile_mass=6.0,
            projectile_charge=3.0,
            target_density=1.5,
            electrons=16,
            temperature=250.0,
            blockchain_security_level=512,
            quantum_resistance_factor=2.5,
            cryptographic_strength=1.5,
            network_hash_rate=5e15,
            consensus_difficulty=2.0
        )
        
        high_security_algo = KontourStoppingPowerAlgo(high_security_params, security_mode=True)
        workflow_manager.register("high_security_analyzer", high_security_algo)
        
        logger.info("✅ Default quantum algorithms registered")
        
    except Exception as e:
        logger.error(f"❌ Failed to register default algorithms: {e}")

@app.get("/health", response_model=HealthResponse)
async def health_check():
    """Health check endpoint"""
    global workflow_manager
    
    if not workflow_manager:
        raise HTTPException(status_code=503, detail="Workflow manager not initialized")
    
    health_data = workflow_manager.health_check()
    
    return HealthResponse(
        status=health_data["status"],
        service="quantum-workflow-api",
        timestamp=health_data["timestamp"],
        algorithms_registered=health_data["algorithms_registered"],
        total_executions=health_data["total_executions"],
        platform_connected=health_data["platform_connected"]
    )

@app.get("/algorithms")
async def list_algorithms():
    """List all registered quantum algorithms"""
    global workflow_manager
    
    if not workflow_manager:
        raise HTTPException(status_code=503, detail="Workflow manager not initialized")
    
    return workflow_manager.list_algorithms()

@app.post("/execute")
async def execute_workflow(request: WorkflowExecutionRequest):
    """Execute a quantum workflow"""
    global workflow_manager, metrics_collector
    
    if not workflow_manager:
        raise HTTPException(status_code=503, detail="Workflow manager not initialized")
    
    try:
        # Execute workflow
        result = await workflow_manager.run_async(
            request.algorithm_name,
            request.optimize,
            request.workflow_id
        )
        
        # Collect metrics if collector is available
        if metrics_collector:
            # Performance metrics
            metrics_collector.collect_performance_metrics(
                algorithm_name=request.algorithm_name,
                workflow_id=result.workflow_id,
                execution_time=result.metrics.execution_time,
                memory_usage=result.metrics.memory_usage,
                quantum_gates=result.metrics.quantum_gates,
                classical_ops=1000,  # Estimated
                additional_metrics={"error_rate": 1.0 - result.metrics.accuracy}
            )
            
            # Accuracy metrics
            metrics_collector.collect_accuracy_metrics(
                algorithm_name=request.algorithm_name,
                workflow_id=result.workflow_id,
                fidelity=result.metrics.accuracy,
                circuit_depth=result.metrics.circuit_depth,
                gate_count=result.metrics.quantum_gates,
                additional_metrics={
                    "quantum_volume": result.metrics.quantum_volume,
                    "noise_resilience": result.metrics.noise_resilience
                }
            )
            
            # Security metrics for security algorithms
            if "security" in request.algorithm_name:
                security_analysis = result.results.get("security_analysis", {})
                attack_resistance = result.results.get("attack_resistance", {})
                
                metrics_collector.collect_security_metrics(
                    algorithm_name=request.algorithm_name,
                    workflow_id=result.workflow_id,
                    quantum_resistance=security_analysis.get("quantum_resistance", 0.8),
                    crypto_strength=security_analysis.get("cryptographic_strength", 0.9),
                    attack_resistance=attack_resistance.get("individual_attacks", {}),
                    additional_metrics={"compliance": 0.95}
                )
        
        return result
        
    except Exception as e:
        logger.error(f"Workflow execution failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/results/{workflow_id}")
async def get_workflow_result(workflow_id: str):
    """Get workflow result by ID"""
    global workflow_manager
    
    if not workflow_manager:
        raise HTTPException(status_code=503, detail="Workflow manager not initialized")
    
    result = workflow_manager.get_result(workflow_id)
    if not result:
        raise HTTPException(status_code=404, detail="Workflow result not found")
    
    return result

@app.get("/report")
async def get_metrics_report():
    """Get comprehensive metrics report"""
    global workflow_manager, metrics_collector
    
    if not workflow_manager:
        raise HTTPException(status_code=503, detail="Workflow manager not initialized")
    
    # Get workflow manager report
    workflow_report = workflow_manager.report()
    
    # Get metrics collector report if available
    metrics_report = {}
    if metrics_collector:
        metrics_report = metrics_collector.generate_report()
    
    return {
        "workflow_manager": workflow_report,
        "metrics_collector": metrics_report,
        "timestamp": datetime.now().isoformat()
    }

@app.get("/metrics/aggregated")
async def get_aggregated_metrics(
    window: str = "1hour",
    algorithm_name: Optional[str] = None
):
    """Get aggregated metrics for specified time window"""
    global metrics_collector
    
    if not metrics_collector:
        raise HTTPException(status_code=503, detail="Metrics collector not initialized")
    
    try:
        return metrics_collector.get_aggregated_metrics(window, algorithm_name)
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))

@app.get("/alerts")
async def get_alerts(level: Optional[str] = None):
    """Get active alerts"""
    global metrics_collector
    
    if not metrics_collector:
        raise HTTPException(status_code=503, detail="Metrics collector not initialized")
    
    from metrics import AlertLevel
    
    alert_level = None
    if level:
        try:
            alert_level = AlertLevel(level.lower())
        except ValueError:
            raise HTTPException(status_code=400, detail=f"Invalid alert level: {level}")
    
    return metrics_collector.get_alerts(alert_level)

@app.post("/demo")
async def run_quantum_demo(background_tasks: BackgroundTasks):
    """Run comprehensive quantum workflow demonstration"""
    global workflow_manager
    
    if not workflow_manager:
        raise HTTPException(status_code=503, detail="Workflow manager not initialized")
    
    try:
        # Import and run demo
        from kontour_demo import run_kontour_quantum_demo
        
        # Run demo in background
        result = await run_kontour_quantum_demo()
        
        return result
        
    except Exception as e:
        logger.error(f"Demo execution failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/algorithms/register")
async def register_algorithm(request: AlgorithmRegistrationRequest):
    """Register a new quantum algorithm"""
    global workflow_manager
    
    if not workflow_manager:
        raise HTTPException(status_code=503, detail="Workflow manager not initialized")
    
    try:
        # Create algorithm based on type
        if request.algorithm_type == "autoencoder":
            algorithm = KontourQuantumAutoencoder(
                qubits=request.parameters.get("qubits", 8),
                window=request.parameters.get("window", 128),
                market_mode=request.parameters.get("market_mode", True)
            )
        elif request.algorithm_type == "stopping_power":
            params = KontourSystemParams(**request.parameters)
            algorithm = KontourStoppingPowerAlgo(params, security_mode=True)
        else:
            raise HTTPException(status_code=400, detail=f"Unknown algorithm type: {request.algorithm_type}")
        
        # Register algorithm
        workflow_manager.register(request.name, algorithm)
        
        return {
            "status": "registered",
            "name": request.name,
            "type": request.algorithm_type,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Algorithm registration failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.delete("/algorithms/{algorithm_name}")
async def unregister_algorithm(algorithm_name: str):
    """Unregister a quantum algorithm"""
    global workflow_manager
    
    if not workflow_manager:
        raise HTTPException(status_code=503, detail="Workflow manager not initialized")
    
    if algorithm_name not in workflow_manager._algos:
        raise HTTPException(status_code=404, detail="Algorithm not found")
    
    # Remove algorithm
    del workflow_manager._algos[algorithm_name]
    
    return {
        "status": "unregistered",
        "name": algorithm_name,
        "timestamp": datetime.now().isoformat()
    }

@app.get("/status")
async def get_system_status():
    """Get comprehensive system status"""
    global workflow_manager, metrics_collector
    
    status = {
        "service": "quantum-workflow-api",
        "timestamp": datetime.now().isoformat(),
        "workflow_manager": {
            "initialized": workflow_manager is not None,
            "algorithms_registered": len(workflow_manager._algos) if workflow_manager else 0,
            "platform_connected": workflow_manager.platform_connected if workflow_manager else False
        },
        "metrics_collector": {
            "initialized": metrics_collector is not None,
            "metrics_collected": len(metrics_collector.metrics_history) if metrics_collector else 0
        }
    }
    
    return status

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8090)
