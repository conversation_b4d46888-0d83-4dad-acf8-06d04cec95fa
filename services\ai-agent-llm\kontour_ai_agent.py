"""
Kontour Coin AI Agent with LLM Integration
Complete AI Agent system with multiple LLM models for cryptocurrency platform
Supports OpenAI GPT-4, Claude, <PERSON>, and local models
"""

import asyncio
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass, asdict
from enum import Enum
import openai
import anthropic
import google.generativeai as genai
from transformers import AutoToken<PERSON>, AutoModelForCausalLM, pipeline
import torch
from fastapi import FastAPI, HTTPException, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
import redis
from kafka import KafkaProducer
import httpx
import os
from dotenv import load_dotenv

load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class LLMProvider(Enum):
    OPENAI_GPT4 = "openai_gpt4"
    OPENAI_GPT35 = "openai_gpt35"
    CLAUDE_3 = "claude_3"
    GEMINI_PRO = "gemini_pro"
    LOCAL_LLAMA = "local_llama"
    DEEPSEEK = "deepseek"

class AgentRole(Enum):
    TRADING_ADVISOR = "trading_advisor"
    MARKET_ANALYST = "market_analyst"
    RISK_MANAGER = "risk_manager"
    PORTFOLIO_OPTIMIZER = "portfolio_optimizer"
    NEWS_ANALYST = "news_analyst"
    TECHNICAL_ANALYST = "technical_analyst"
    SENTIMENT_ANALYZER = "sentiment_analyzer"
    BLOCKCHAIN_MONITOR = "blockchain_monitor"

@dataclass
class AIAgentConfig:
    """AI Agent configuration"""
    agent_id: str
    name: str
    role: AgentRole
    llm_provider: LLMProvider
    model_name: str
    temperature: float = 0.7
    max_tokens: int = 1000
    system_prompt: str = ""
    enabled: bool = True

@dataclass
class AgentMessage:
    """Message structure for AI Agent communication"""
    message_id: str
    agent_id: str
    user_id: str
    content: str
    message_type: str
    timestamp: datetime
    context: Dict[str, Any] = None
    metadata: Dict[str, Any] = None

@dataclass
class AgentResponse:
    """AI Agent response structure"""
    response_id: str
    agent_id: str
    message_id: str
    content: str
    confidence: float
    reasoning: str
    recommendations: List[str]
    timestamp: datetime
    processing_time: float
    model_used: str

class KontourAIAgent:
    """Individual AI Agent with LLM capabilities"""
    
    def __init__(self, config: AIAgentConfig):
        self.config = config
        self.conversation_history: List[Dict] = []
        self.performance_metrics = {
            "total_requests": 0,
            "successful_responses": 0,
            "average_response_time": 0.0,
            "accuracy_score": 0.0
        }
        
        # Initialize LLM client based on provider
        self._initialize_llm_client()
        
    def _initialize_llm_client(self):
        """Initialize the appropriate LLM client"""
        try:
            if self.config.llm_provider == LLMProvider.OPENAI_GPT4:
                openai.api_key = os.getenv('OPENAI_API_KEY')
                self.client = openai
                
            elif self.config.llm_provider == LLMProvider.CLAUDE_3:
                self.client = anthropic.Anthropic(
                    api_key=os.getenv('ANTHROPIC_API_KEY')
                )
                
            elif self.config.llm_provider == LLMProvider.GEMINI_PRO:
                genai.configure(api_key=os.getenv('GOOGLE_API_KEY'))
                self.client = genai.GenerativeModel('gemini-pro')
                
            elif self.config.llm_provider == LLMProvider.LOCAL_LLAMA:
                self._initialize_local_model()
                
            elif self.config.llm_provider == LLMProvider.DEEPSEEK:
                self.client = openai
                openai.api_base = "https://api.deepseek.com/v1"
                openai.api_key = os.getenv('DEEPSEEK_API_KEY')
                
            logger.info(f"Initialized LLM client for {self.config.llm_provider.value}")
            
        except Exception as e:
            logger.error(f"Failed to initialize LLM client: {e}")
            raise
    
    def _initialize_local_model(self):
        """Initialize local LLaMA model"""
        try:
            model_name = "microsoft/DialoGPT-medium"  # Fallback to smaller model
            self.tokenizer = AutoTokenizer.from_pretrained(model_name)
            self.model = AutoModelForCausalLM.from_pretrained(model_name)
            self.pipeline = pipeline(
                "text-generation",
                model=self.model,
                tokenizer=self.tokenizer,
                device=0 if torch.cuda.is_available() else -1
            )
            logger.info("Local model initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize local model: {e}")
            # Fallback to a simpler approach
            self.pipeline = None
    
    async def process_message(self, message: AgentMessage) -> AgentResponse:
        """Process incoming message and generate AI response"""
        start_time = datetime.now()
        
        try:
            # Add context to conversation history
            self.conversation_history.append({
                "role": "user",
                "content": message.content,
                "timestamp": message.timestamp.isoformat(),
                "context": message.context or {}
            })
            
            # Generate response based on LLM provider
            response_content = await self._generate_llm_response(message)
            
            # Extract recommendations and reasoning
            recommendations = self._extract_recommendations(response_content)
            reasoning = self._extract_reasoning(response_content)
            confidence = self._calculate_confidence(response_content, message.context)
            
            # Create response
            processing_time = (datetime.now() - start_time).total_seconds()
            
            response = AgentResponse(
                response_id=f"resp_{int(datetime.now().timestamp())}",
                agent_id=self.config.agent_id,
                message_id=message.message_id,
                content=response_content,
                confidence=confidence,
                reasoning=reasoning,
                recommendations=recommendations,
                timestamp=datetime.now(),
                processing_time=processing_time,
                model_used=f"{self.config.llm_provider.value}:{self.config.model_name}"
            )
            
            # Update conversation history
            self.conversation_history.append({
                "role": "assistant",
                "content": response_content,
                "timestamp": response.timestamp.isoformat(),
                "confidence": confidence
            })
            
            # Update performance metrics
            self._update_performance_metrics(processing_time, True)
            
            return response
            
        except Exception as e:
            logger.error(f"Error processing message: {e}")
            self._update_performance_metrics(0, False)
            raise
    
    async def _generate_llm_response(self, message: AgentMessage) -> str:
        """Generate response using the configured LLM"""
        
        # Prepare system prompt based on agent role
        system_prompt = self._get_role_specific_prompt(message.context)
        
        # Prepare conversation context
        messages = [{"role": "system", "content": system_prompt}]
        
        # Add recent conversation history (last 10 messages)
        recent_history = self.conversation_history[-10:] if self.conversation_history else []
        messages.extend(recent_history)
        
        # Add current message
        messages.append({"role": "user", "content": message.content})
        
        try:
            if self.config.llm_provider == LLMProvider.OPENAI_GPT4:
                response = await self._call_openai(messages)
                
            elif self.config.llm_provider == LLMProvider.CLAUDE_3:
                response = await self._call_claude(messages)
                
            elif self.config.llm_provider == LLMProvider.GEMINI_PRO:
                response = await self._call_gemini(messages)
                
            elif self.config.llm_provider == LLMProvider.LOCAL_LLAMA:
                response = await self._call_local_model(messages)
                
            elif self.config.llm_provider == LLMProvider.DEEPSEEK:
                response = await self._call_deepseek(messages)
                
            else:
                raise ValueError(f"Unsupported LLM provider: {self.config.llm_provider}")
                
            return response
            
        except Exception as e:
            logger.error(f"LLM generation failed: {e}")
            return f"I apologize, but I'm experiencing technical difficulties. Error: {str(e)}"
    
    async def _call_openai(self, messages: List[Dict]) -> str:
        """Call OpenAI API"""
        try:
            response = await openai.ChatCompletion.acreate(
                model=self.config.model_name or "gpt-4",
                messages=messages,
                temperature=self.config.temperature,
                max_tokens=self.config.max_tokens
            )
            return response.choices[0].message.content
        except Exception as e:
            logger.error(f"OpenAI API call failed: {e}")
            raise
    
    async def _call_claude(self, messages: List[Dict]) -> str:
        """Call Anthropic Claude API"""
        try:
            # Convert messages to Claude format
            prompt = self._convert_messages_to_claude_format(messages)
            
            response = await self.client.completions.create(
                model=self.config.model_name or "claude-3-sonnet-20240229",
                prompt=prompt,
                max_tokens_to_sample=self.config.max_tokens,
                temperature=self.config.temperature
            )
            return response.completion
        except Exception as e:
            logger.error(f"Claude API call failed: {e}")
            raise
    
    async def _call_gemini(self, messages: List[Dict]) -> str:
        """Call Google Gemini API"""
        try:
            # Convert messages to Gemini format
            prompt = self._convert_messages_to_gemini_format(messages)
            
            response = await self.client.generate_content_async(
                prompt,
                generation_config=genai.types.GenerationConfig(
                    temperature=self.config.temperature,
                    max_output_tokens=self.config.max_tokens
                )
            )
            return response.text
        except Exception as e:
            logger.error(f"Gemini API call failed: {e}")
            raise
    
    async def _call_local_model(self, messages: List[Dict]) -> str:
        """Call local model"""
        try:
            if not self.pipeline:
                return "Local model not available. Please check the model configuration."
            
            # Convert messages to simple prompt
            prompt = self._convert_messages_to_simple_format(messages)
            
            # Generate response
            response = self.pipeline(
                prompt,
                max_length=len(prompt.split()) + self.config.max_tokens,
                temperature=self.config.temperature,
                do_sample=True,
                pad_token_id=self.tokenizer.eos_token_id
            )
            
            # Extract generated text
            generated_text = response[0]['generated_text']
            # Remove the original prompt from the response
            response_text = generated_text[len(prompt):].strip()
            
            return response_text or "I understand your request, but I need more context to provide a detailed response."
            
        except Exception as e:
            logger.error(f"Local model call failed: {e}")
            return "I'm experiencing technical difficulties with the local model."
    
    async def _call_deepseek(self, messages: List[Dict]) -> str:
        """Call DeepSeek API"""
        try:
            response = await openai.ChatCompletion.acreate(
                model=self.config.model_name or "deepseek-chat",
                messages=messages,
                temperature=self.config.temperature,
                max_tokens=self.config.max_tokens
            )
            return response.choices[0].message.content
        except Exception as e:
            logger.error(f"DeepSeek API call failed: {e}")
            raise
    
    def _get_role_specific_prompt(self, context: Dict[str, Any] = None) -> str:
        """Get system prompt based on agent role"""
        base_prompt = f"""You are a specialized AI agent for Kontour Coin, a next-generation cryptocurrency platform. 
Your role is: {self.config.role.value.replace('_', ' ').title()}

Current context: {json.dumps(context or {}, indent=2)}

Guidelines:
- Provide accurate, actionable insights
- Consider market volatility and risk factors
- Base recommendations on data and analysis
- Be concise but comprehensive
- Include confidence levels in your assessments
"""

        role_specific_prompts = {
            AgentRole.TRADING_ADVISOR: """
As a Trading Advisor, provide specific trading recommendations including:
- Entry and exit points
- Risk management strategies
- Position sizing recommendations
- Market timing analysis
- Technical and fundamental analysis insights
""",
            AgentRole.MARKET_ANALYST: """
As a Market Analyst, focus on:
- Market trend analysis
- Price prediction and forecasting
- Market sentiment evaluation
- Comparative analysis with other cryptocurrencies
- Economic factor impacts
""",
            AgentRole.RISK_MANAGER: """
As a Risk Manager, emphasize:
- Risk assessment and quantification
- Portfolio risk analysis
- Stress testing scenarios
- Risk mitigation strategies
- Compliance and regulatory considerations
""",
            AgentRole.PORTFOLIO_OPTIMIZER: """
As a Portfolio Optimizer, concentrate on:
- Asset allocation recommendations
- Portfolio rebalancing strategies
- Diversification analysis
- Performance optimization
- Risk-adjusted returns
""",
            AgentRole.NEWS_ANALYST: """
As a News Analyst, provide:
- News impact analysis
- Market sentiment from news
- Event-driven trading opportunities
- Regulatory news implications
- Social media sentiment analysis
""",
            AgentRole.TECHNICAL_ANALYST: """
As a Technical Analyst, focus on:
- Chart pattern analysis
- Technical indicator interpretations
- Support and resistance levels
- Volume analysis
- Price action predictions
""",
            AgentRole.SENTIMENT_ANALYZER: """
As a Sentiment Analyzer, analyze:
- Market sentiment indicators
- Social media sentiment
- Fear and greed index implications
- Crowd psychology factors
- Contrarian opportunities
""",
            AgentRole.BLOCKCHAIN_MONITOR: """
As a Blockchain Monitor, track:
- On-chain metrics and analysis
- Network health indicators
- Transaction patterns
- Whale movements
- Protocol updates and implications
"""
        }
        
        return base_prompt + role_specific_prompts.get(self.config.role, "")
    
    def _convert_messages_to_claude_format(self, messages: List[Dict]) -> str:
        """Convert messages to Claude prompt format"""
        prompt = ""
        for msg in messages:
            if msg["role"] == "system":
                prompt += f"System: {msg['content']}\n\n"
            elif msg["role"] == "user":
                prompt += f"Human: {msg['content']}\n\n"
            elif msg["role"] == "assistant":
                prompt += f"Assistant: {msg['content']}\n\n"
        
        prompt += "Assistant: "
        return prompt
    
    def _convert_messages_to_gemini_format(self, messages: List[Dict]) -> str:
        """Convert messages to Gemini prompt format"""
        prompt = ""
        for msg in messages:
            if msg["role"] == "system":
                prompt += f"Instructions: {msg['content']}\n\n"
            elif msg["role"] == "user":
                prompt += f"User: {msg['content']}\n\n"
            elif msg["role"] == "assistant":
                prompt += f"Assistant: {msg['content']}\n\n"
        
        return prompt
    
    def _convert_messages_to_simple_format(self, messages: List[Dict]) -> str:
        """Convert messages to simple prompt format for local models"""
        prompt = ""
        for msg in messages:
            if msg["role"] == "system":
                prompt += f"{msg['content']}\n\n"
            elif msg["role"] == "user":
                prompt += f"User: {msg['content']}\n"
            elif msg["role"] == "assistant":
                prompt += f"Assistant: {msg['content']}\n"
        
        prompt += "Assistant: "
        return prompt
    
    def _extract_recommendations(self, response: str) -> List[str]:
        """Extract actionable recommendations from response"""
        recommendations = []
        
        # Look for common recommendation patterns
        lines = response.split('\n')
        for line in lines:
            line = line.strip()
            if any(keyword in line.lower() for keyword in ['recommend', 'suggest', 'advise', 'should', 'consider']):
                if len(line) > 20:  # Filter out very short lines
                    recommendations.append(line)
        
        # If no specific recommendations found, extract key points
        if not recommendations:
            sentences = response.split('.')
            for sentence in sentences:
                sentence = sentence.strip()
                if any(keyword in sentence.lower() for keyword in ['buy', 'sell', 'hold', 'invest', 'avoid']):
                    if len(sentence) > 30:
                        recommendations.append(sentence + '.')
        
        return recommendations[:5]  # Limit to top 5 recommendations
    
    def _extract_reasoning(self, response: str) -> str:
        """Extract reasoning from response"""
        # Look for reasoning indicators
        reasoning_keywords = ['because', 'due to', 'analysis shows', 'based on', 'given that']
        
        sentences = response.split('.')
        reasoning_sentences = []
        
        for sentence in sentences:
            if any(keyword in sentence.lower() for keyword in reasoning_keywords):
                reasoning_sentences.append(sentence.strip())
        
        if reasoning_sentences:
            return '. '.join(reasoning_sentences[:3]) + '.'
        
        # Fallback: return first few sentences as reasoning
        return '. '.join(sentences[:2]) + '.' if len(sentences) >= 2 else response[:200] + '...'
    
    def _calculate_confidence(self, response: str, context: Dict[str, Any] = None) -> float:
        """Calculate confidence score for the response"""
        confidence = 0.5  # Base confidence
        
        # Increase confidence based on response characteristics
        if len(response) > 100:
            confidence += 0.1
        
        if any(keyword in response.lower() for keyword in ['analysis', 'data', 'trend', 'pattern']):
            confidence += 0.1
        
        if any(keyword in response.lower() for keyword in ['recommend', 'suggest', 'advise']):
            confidence += 0.1
        
        # Decrease confidence for uncertainty indicators
        if any(keyword in response.lower() for keyword in ['uncertain', 'unclear', 'maybe', 'possibly']):
            confidence -= 0.2
        
        # Adjust based on context availability
        if context and len(context) > 0:
            confidence += 0.1
        
        return max(0.1, min(0.95, confidence))  # Clamp between 0.1 and 0.95
    
    def _update_performance_metrics(self, processing_time: float, success: bool):
        """Update agent performance metrics"""
        self.performance_metrics["total_requests"] += 1
        
        if success:
            self.performance_metrics["successful_responses"] += 1
            
            # Update average response time
            current_avg = self.performance_metrics["average_response_time"]
            total_requests = self.performance_metrics["total_requests"]
            self.performance_metrics["average_response_time"] = (
                (current_avg * (total_requests - 1) + processing_time) / total_requests
            )
    
    def get_performance_metrics(self) -> Dict[str, Any]:
        """Get agent performance metrics"""
        success_rate = 0
        if self.performance_metrics["total_requests"] > 0:
            success_rate = (
                self.performance_metrics["successful_responses"] / 
                self.performance_metrics["total_requests"]
            )
        
        return {
            **self.performance_metrics,
            "success_rate": success_rate,
            "agent_id": self.config.agent_id,
            "agent_role": self.config.role.value,
            "llm_provider": self.config.llm_provider.value
        }
    
    def clear_conversation_history(self):
        """Clear conversation history"""
        self.conversation_history = []
    
    def get_conversation_summary(self) -> Dict[str, Any]:
        """Get conversation summary"""
        return {
            "agent_id": self.config.agent_id,
            "total_messages": len(self.conversation_history),
            "recent_messages": self.conversation_history[-5:] if self.conversation_history else [],
            "last_interaction": self.conversation_history[-1]["timestamp"] if self.conversation_history else None
        }
