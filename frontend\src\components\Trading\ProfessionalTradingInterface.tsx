import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Button,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Tabs,
  Tab,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  Avatar,
  IconButton,
  Tooltip,
  Paper,
  Divider,
  Switch,
  FormControlLabel,
  Slider,
  useTheme,
  Alert,
  LinearProgress
} from '@mui/material';
import {
  TrendingUp as BuyIcon,
  TrendingDown as SellIcon,
  Psychology as AIIcon,
  Speed as QuickIcon,
  Timeline as ChartIcon,
  Refresh as RefreshIcon,
  Settings as SettingsIcon,
  Notifications as AlertIcon,
  AutoAwesome as AutoIcon,
  Security as SecurityIcon,
  Assessment as AnalyticsIcon,
  AccountBalance as BalanceIcon
} from '@mui/icons-material';
import { styled, alpha } from '@mui/material/styles';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, CandlestickChart } from 'recharts';

// Styled Components
const TradingCard = styled(Card)(({ theme }) => ({
  background: alpha(theme.palette.background.paper, 0.9),
  backdropFilter: 'blur(20px)',
  border: `1px solid ${alpha(theme.palette.divider, 0.12)}`,
  borderRadius: 16,
  overflow: 'hidden',
}));

const OrderBookCard = styled(Card)(({ theme }) => ({
  background: `linear-gradient(135deg, ${alpha(theme.palette.background.paper, 0.95)} 0%, ${alpha(theme.palette.background.paper, 0.85)} 100%)`,
  backdropFilter: 'blur(20px)',
  border: `1px solid ${alpha(theme.palette.divider, 0.08)}`,
  borderRadius: 16,
}));

const PriceDisplay = styled(Typography)<{ trend: 'up' | 'down' | 'neutral' }>(({ theme, trend }) => ({
  fontWeight: 700,
  fontSize: '2rem',
  color: trend === 'up' ? theme.palette.success.main : 
        trend === 'down' ? theme.palette.error.main : 
        theme.palette.text.primary,
}));

const OrderButton = styled(Button)<{ orderType: 'buy' | 'sell' }>(({ theme, orderType }) => ({
  borderRadius: 12,
  padding: '12px 24px',
  fontWeight: 600,
  textTransform: 'none',
  background: orderType === 'buy' 
    ? `linear-gradient(135deg, ${theme.palette.success.main} 0%, ${theme.palette.success.dark} 100%)`
    : `linear-gradient(135deg, ${theme.palette.error.main} 0%, ${theme.palette.error.dark} 100%)`,
  '&:hover': {
    background: orderType === 'buy' 
      ? `linear-gradient(135deg, ${theme.palette.success.dark} 0%, ${theme.palette.success.main} 100%)`
      : `linear-gradient(135deg, ${theme.palette.error.dark} 0%, ${theme.palette.error.main} 100%)`,
  },
}));

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const TabPanel: React.FC<TabPanelProps> = ({ children, value, index }) => (
  <div hidden={value !== index}>
    {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
  </div>
);

const ProfessionalTradingInterface: React.FC = () => {
  const theme = useTheme();
  const [selectedPair, setSelectedPair] = useState('BTC/USDT');
  const [orderType, setOrderType] = useState<'buy' | 'sell'>('buy');
  const [tradeType, setTradeType] = useState<'market' | 'limit' | 'stop'>('market');
  const [amount, setAmount] = useState('');
  const [price, setPrice] = useState('');
  const [tabValue, setTabValue] = useState(0);
  const [aiEnabled, setAiEnabled] = useState(true);
  const [autoTrade, setAutoTrade] = useState(false);

  // Mock data
  const tradingPairs = [
    { symbol: 'BTC/USDT', price: 43250.50, change: 2.45, volume: '1.2B' },
    { symbol: 'ETH/USDT', price: 2650.75, change: -1.23, volume: '890M' },
    { symbol: 'KTC/USDT', price: 2.45, change: 15.67, volume: '45M' },
    { symbol: 'ADA/USDT', price: 0.485, change: 3.21, volume: '234M' },
  ];

  const orderBook = {
    asks: [
      { price: 43255.50, amount: 0.245, total: 10.59 },
      { price: 43254.25, amount: 0.156, total: 6.75 },
      { price: 43253.00, amount: 0.089, total: 3.85 },
      { price: 43252.75, amount: 0.234, total: 10.12 },
      { price: 43251.50, amount: 0.167, total: 7.22 },
    ],
    bids: [
      { price: 43249.75, amount: 0.189, total: 8.17 },
      { price: 43248.50, amount: 0.267, total: 11.54 },
      { price: 43247.25, amount: 0.145, total: 6.27 },
      { price: 43246.00, amount: 0.298, total: 12.89 },
      { price: 43245.75, amount: 0.178, total: 7.70 },
    ]
  };

  const recentTrades = [
    { price: 43250.50, amount: 0.125, time: '14:32:15', type: 'buy' },
    { price: 43249.75, amount: 0.089, time: '14:32:12', type: 'sell' },
    { price: 43251.25, amount: 0.234, time: '14:32:08', type: 'buy' },
    { price: 43248.90, amount: 0.156, time: '14:32:05', type: 'sell' },
    { price: 43252.00, amount: 0.078, time: '14:32:02', type: 'buy' },
  ];

  const aiRecommendations = [
    { type: 'buy', confidence: 87, reason: 'Strong bullish momentum detected', timeframe: '4h' },
    { type: 'hold', confidence: 92, reason: 'Consolidation phase, wait for breakout', timeframe: '1d' },
    { type: 'sell', confidence: 76, reason: 'Resistance level approaching', timeframe: '1h' },
  ];

  const portfolioBalance = {
    USDT: 15420.50,
    BTC: 0.245,
    ETH: 1.567,
    KTC: 2450.00
  };

  const handleOrderSubmit = () => {
    // Handle order submission
    console.log('Order submitted:', { orderType, tradeType, amount, price, selectedPair });
  };

  const handleQuickTrade = (type: 'buy' | 'sell', percentage: number) => {
    const balance = type === 'buy' ? portfolioBalance.USDT : portfolioBalance.BTC;
    const tradeAmount = (balance * percentage / 100).toFixed(6);
    setAmount(tradeAmount);
    setOrderType(type);
  };

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ mb: 3, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Box>
          <Typography variant="h4" fontWeight={700} gutterBottom>
            Professional Trading 📈
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Advanced trading interface with AI-powered insights
          </Typography>
        </Box>
        <Box sx={{ display: 'flex', gap: 1 }}>
          <FormControlLabel
            control={<Switch checked={aiEnabled} onChange={(e) => setAiEnabled(e.target.checked)} />}
            label="AI Assistant"
          />
          <FormControlLabel
            control={<Switch checked={autoTrade} onChange={(e) => setAutoTrade(e.target.checked)} />}
            label="Auto Trade"
          />
          <Button variant="outlined" startIcon={<RefreshIcon />}>
            Refresh
          </Button>
        </Box>
      </Box>

      <Grid container spacing={3}>
        {/* Trading Pairs */}
        <Grid item xs={12} md={3}>
          <TradingCard>
            <CardContent>
              <Typography variant="h6" fontWeight={600} gutterBottom>
                Markets
              </Typography>
              {tradingPairs.map((pair) => (
                <Box
                  key={pair.symbol}
                  sx={{
                    p: 2,
                    mb: 1,
                    borderRadius: 2,
                    cursor: 'pointer',
                    bgcolor: selectedPair === pair.symbol ? alpha(theme.palette.primary.main, 0.1) : 'transparent',
                    border: selectedPair === pair.symbol ? `1px solid ${theme.palette.primary.main}` : '1px solid transparent',
                    '&:hover': { bgcolor: alpha(theme.palette.primary.main, 0.05) }
                  }}
                  onClick={() => setSelectedPair(pair.symbol)}
                >
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                    <Typography variant="subtitle2" fontWeight={600}>
                      {pair.symbol}
                    </Typography>
                    <Chip
                      label={`${pair.change > 0 ? '+' : ''}${pair.change}%`}
                      size="small"
                      color={pair.change > 0 ? 'success' : 'error'}
                    />
                  </Box>
                  <Typography variant="h6" fontWeight={700}>
                    ${pair.price.toLocaleString()}
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    Vol: {pair.volume}
                  </Typography>
                </Box>
              ))}
            </CardContent>
          </TradingCard>
        </Grid>

        {/* Chart and Order Book */}
        <Grid item xs={12} md={6}>
          <TradingCard sx={{ mb: 3 }}>
            <CardContent>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Box>
                  <Typography variant="h6" fontWeight={600}>
                    {selectedPair}
                  </Typography>
                  <PriceDisplay trend="up">
                    $43,250.50
                  </PriceDisplay>
                  <Typography variant="body2" color="success.main">
                    +$1,025.50 (+2.45%) 24h
                  </Typography>
                </Box>
                <Box sx={{ display: 'flex', gap: 1 }}>
                  <IconButton><ChartIcon /></IconButton>
                  <IconButton><AnalyticsIcon /></IconButton>
                  <IconButton><SettingsIcon /></IconButton>
                </Box>
              </Box>
              <ResponsiveContainer width="100%" height={300}>
                <LineChart data={[
                  { time: '09:00', price: 42800 },
                  { time: '10:00', price: 43100 },
                  { time: '11:00', price: 42950 },
                  { time: '12:00', price: 43200 },
                  { time: '13:00', price: 43050 },
                  { time: '14:00', price: 43250 },
                ]}>
                  <CartesianGrid strokeDasharray="3 3" stroke={alpha(theme.palette.divider, 0.3)} />
                  <XAxis dataKey="time" stroke={theme.palette.text.secondary} />
                  <YAxis stroke={theme.palette.text.secondary} />
                  <Tooltip 
                    contentStyle={{
                      backgroundColor: theme.palette.background.paper,
                      border: `1px solid ${alpha(theme.palette.divider, 0.12)}`,
                      borderRadius: 8,
                    }}
                  />
                  <Line 
                    type="monotone" 
                    dataKey="price" 
                    stroke={theme.palette.success.main}
                    strokeWidth={2}
                    dot={false}
                  />
                </LineChart>
              </ResponsiveContainer>
            </CardContent>
          </TradingCard>

          {/* Order Book */}
          <OrderBookCard>
            <CardContent>
              <Typography variant="h6" fontWeight={600} gutterBottom>
                Order Book
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={6}>
                  <Typography variant="subtitle2" color="error.main" gutterBottom>
                    Asks (Sell Orders)
                  </Typography>
                  {orderBook.asks.map((ask, index) => (
                    <Box key={index} sx={{ display: 'flex', justifyContent: 'space-between', py: 0.5 }}>
                      <Typography variant="body2" color="error.main">
                        ${ask.price.toLocaleString()}
                      </Typography>
                      <Typography variant="body2">
                        {ask.amount}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        {ask.total}
                      </Typography>
                    </Box>
                  ))}
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="subtitle2" color="success.main" gutterBottom>
                    Bids (Buy Orders)
                  </Typography>
                  {orderBook.bids.map((bid, index) => (
                    <Box key={index} sx={{ display: 'flex', justifyContent: 'space-between', py: 0.5 }}>
                      <Typography variant="body2" color="success.main">
                        ${bid.price.toLocaleString()}
                      </Typography>
                      <Typography variant="body2">
                        {bid.amount}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        {bid.total}
                      </Typography>
                    </Box>
                  ))}
                </Grid>
              </Grid>
            </CardContent>
          </OrderBookCard>
        </Grid>

        {/* Trading Panel */}
        <Grid item xs={12} md={3}>
          <TradingCard>
            <CardContent>
              <Tabs value={tabValue} onChange={(_, newValue) => setTabValue(newValue)} sx={{ mb: 2 }}>
                <Tab label="Spot" />
                <Tab label="Margin" />
              </Tabs>

              <TabPanel value={tabValue} index={0}>
                {/* Order Type Selection */}
                <Box sx={{ display: 'flex', gap: 1, mb: 2 }}>
                  <Button
                    variant={orderType === 'buy' ? 'contained' : 'outlined'}
                    color="success"
                    onClick={() => setOrderType('buy')}
                    sx={{ flex: 1 }}
                  >
                    Buy
                  </Button>
                  <Button
                    variant={orderType === 'sell' ? 'contained' : 'outlined'}
                    color="error"
                    onClick={() => setOrderType('sell')}
                    sx={{ flex: 1 }}
                  >
                    Sell
                  </Button>
                </Box>

                {/* Trade Type */}
                <FormControl fullWidth sx={{ mb: 2 }}>
                  <InputLabel>Order Type</InputLabel>
                  <Select
                    value={tradeType}
                    onChange={(e) => setTradeType(e.target.value as any)}
                  >
                    <MenuItem value="market">Market</MenuItem>
                    <MenuItem value="limit">Limit</MenuItem>
                    <MenuItem value="stop">Stop</MenuItem>
                  </Select>
                </FormControl>

                {/* Price Input */}
                {tradeType !== 'market' && (
                  <TextField
                    fullWidth
                    label="Price (USDT)"
                    value={price}
                    onChange={(e) => setPrice(e.target.value)}
                    sx={{ mb: 2 }}
                  />
                )}

                {/* Amount Input */}
                <TextField
                  fullWidth
                  label={`Amount (${selectedPair.split('/')[0]})`}
                  value={amount}
                  onChange={(e) => setAmount(e.target.value)}
                  sx={{ mb: 2 }}
                />

                {/* Quick Trade Buttons */}
                <Box sx={{ display: 'flex', gap: 1, mb: 2 }}>
                  {[25, 50, 75, 100].map((percentage) => (
                    <Button
                      key={percentage}
                      size="small"
                      variant="outlined"
                      onClick={() => handleQuickTrade(orderType, percentage)}
                      sx={{ flex: 1 }}
                    >
                      {percentage}%
                    </Button>
                  ))}
                </Box>

                {/* Balance Display */}
                <Box sx={{ mb: 2, p: 2, bgcolor: alpha(theme.palette.background.default, 0.5), borderRadius: 2 }}>
                  <Typography variant="body2" color="text.secondary" gutterBottom>
                    Available Balance
                  </Typography>
                  <Typography variant="body2" fontWeight={600}>
                    {orderType === 'buy' 
                      ? `${portfolioBalance.USDT.toLocaleString()} USDT`
                      : `${portfolioBalance.BTC} BTC`
                    }
                  </Typography>
                </Box>

                {/* Submit Order */}
                <OrderButton
                  fullWidth
                  orderType={orderType}
                  onClick={handleOrderSubmit}
                  startIcon={orderType === 'buy' ? <BuyIcon /> : <SellIcon />}
                >
                  {orderType === 'buy' ? 'Buy' : 'Sell'} {selectedPair.split('/')[0]}
                </OrderButton>
              </TabPanel>

              <TabPanel value={tabValue} index={1}>
                <Alert severity="info" sx={{ mb: 2 }}>
                  Margin trading coming soon
                </Alert>
              </TabPanel>
            </CardContent>
          </TradingCard>

          {/* AI Recommendations */}
          {aiEnabled && (
            <TradingCard sx={{ mt: 2 }}>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <AIIcon sx={{ mr: 1, color: theme.palette.primary.main }} />
                  <Typography variant="h6" fontWeight={600}>
                    AI Insights
                  </Typography>
                </Box>
                {aiRecommendations.map((rec, index) => (
                  <Box key={index} sx={{ mb: 2, p: 2, bgcolor: alpha(theme.palette.background.default, 0.3), borderRadius: 2 }}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                      <Chip
                        label={rec.type.toUpperCase()}
                        size="small"
                        color={rec.type === 'buy' ? 'success' : rec.type === 'sell' ? 'error' : 'warning'}
                      />
                      <Typography variant="caption">
                        {rec.timeframe}
                      </Typography>
                    </Box>
                    <Typography variant="body2" sx={{ mb: 1 }}>
                      {rec.reason}
                    </Typography>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Typography variant="caption">
                        Confidence: {rec.confidence}%
                      </Typography>
                      <LinearProgress
                        variant="determinate"
                        value={rec.confidence}
                        sx={{ flex: 1, height: 4, borderRadius: 2 }}
                      />
                    </Box>
                  </Box>
                ))}
              </CardContent>
            </TradingCard>
          )}
        </Grid>
      </Grid>
    </Box>
  );
};

export default ProfessionalTradingInterface;
