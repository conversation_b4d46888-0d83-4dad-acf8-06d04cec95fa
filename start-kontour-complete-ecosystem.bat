@echo off
setlocal enabledelayedexpansion
title Kontour Coin Complete Ecosystem Launcher
color 0A

echo.
echo ████████████████████████████████████████████████████████████████
echo ██                                                            ██
echo ██    💎 KONTOUR COIN COMPLETE ECOSYSTEM LAUNCHER 💎          ██
echo ██                                                            ██
echo ██    Next-Generation Cryptocurrency Platform                 ██
echo ██    AI • Quantum • Blockchain • Web3 • DeFi                ██
echo ██                                                            ██
echo ████████████████████████████████████████████████████████████████
echo.

:: Set environment variables for production
set NODE_ENV=production
set REACT_APP_API_URL=http://localhost:8080
set REACT_APP_WS_URL=ws://localhost:8030
set REACT_APP_BLOCKCHAIN_URL=http://localhost:8545
set PYTHONPATH=%CD%

:: Initialize error tracking
set ERRORS=0
set WARNINGS=0

:: Helper functions for service management
goto :main

:start_docker_service
set SERVICE_NAME=%~1
set SERVICE_COMMAND=%~2
echo Starting %SERVICE_NAME%...
%SERVICE_COMMAND% >nul 2>&1
if errorlevel 1 (
    echo ⚠️  %SERVICE_NAME% failed to start
    set /a WARNINGS+=1
) else (
    echo ✅ %SERVICE_NAME% started
)
goto :eof

:start_python_service
set SERVICE_NAME=%~1
set SERVICE_PATH=%~2
echo Starting %SERVICE_NAME%...
if exist "%SERVICE_PATH%" (
    start /b "%SERVICE_NAME%" python "%SERVICE_PATH%" >nul 2>&1
    if errorlevel 1 (
        echo ⚠️  %SERVICE_NAME% failed to start
        set /a WARNINGS+=1
    ) else (
        echo ✅ %SERVICE_NAME% started
    )
) else (
    echo ⚠️  %SERVICE_NAME% file not found: %SERVICE_PATH%
    set /a WARNINGS+=1
)
goto :eof

:start_npm_service
set SERVICE_NAME=%~1
set SERVICE_DIR=%~2
set SERVICE_COMMAND=%~3
echo Starting %SERVICE_NAME%...
if exist "%SERVICE_DIR%\package.json" (
    cd "%SERVICE_DIR%"
    start /b "%SERVICE_NAME%" %SERVICE_COMMAND% >nul 2>&1
    if errorlevel 1 (
        echo ⚠️  %SERVICE_NAME% failed to start
        set /a WARNINGS+=1
    ) else (
        echo ✅ %SERVICE_NAME% started
    )
    cd ..
) else (
    echo ⚠️  %SERVICE_NAME% directory not found: %SERVICE_DIR%
    set /a WARNINGS+=1
)
goto :eof

:main

echo 🔍 System Check and Prerequisites
echo ===============================================
echo Checking system requirements...

:: Check Node.js
echo Checking Node.js...
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Node.js not found. Attempting installation...
    winget install OpenJS.NodeJS >nul 2>&1
    if errorlevel 1 (
        echo ❌ Failed to install Node.js automatically.
        echo Please install Node.js manually from https://nodejs.org/
        set /a ERRORS+=1
        echo ⚠️  Continuing without Node.js - some services may fail
    ) else (
        echo ✅ Node.js installed successfully
    )
) else (
    for /f "tokens=*" %%i in ('node --version') do set NODE_VERSION=%%i
    echo ✅ Node.js found: !NODE_VERSION!
)

:: Check Python
echo Checking Python...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python not found. Attempting installation...
    winget install Python.Python.3.11 >nul 2>&1
    if errorlevel 1 (
        echo ❌ Failed to install Python automatically.
        echo Please install Python manually from https://python.org/
        set /a ERRORS+=1
        echo ⚠️  Continuing without Python - some services may fail
    ) else (
        echo ✅ Python installed successfully
    )
) else (
    for /f "tokens=*" %%i in ('python --version') do set PYTHON_VERSION=%%i
    echo ✅ Python found: !PYTHON_VERSION!
)

:: Check Docker
echo Checking Docker...
docker --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Docker not found.
    echo Please install Docker Desktop from https://www.docker.com/products/docker-desktop
    set /a ERRORS+=1
    echo ⚠️  Continuing without Docker - infrastructure services may fail
) else (
    for /f "tokens=*" %%i in ('docker --version') do set DOCKER_VERSION=%%i
    echo ✅ Docker found: !DOCKER_VERSION!
)

:: Check npm
echo Checking npm...
npm --version >nul 2>&1
if errorlevel 1 (
    echo ❌ npm not found
    set /a ERRORS+=1
) else (
    for /f "tokens=*" %%i in ('npm --version') do set NPM_VERSION=%%i
    echo ✅ npm found: !NPM_VERSION!
)

:: Check pip
echo Checking pip...
pip --version >nul 2>&1
if errorlevel 1 (
    echo ❌ pip not found
    set /a ERRORS+=1
) else (
    echo ✅ pip found
)

echo.
echo 📦 Installing Dependencies
echo ===============================================

:: Create requirements.txt if it doesn't exist
if not exist "requirements.txt" (
    echo Creating requirements.txt...
    (
        echo fastapi>=0.104.0
        echo uvicorn>=0.24.0
        echo redis>=5.0.0
        echo kafka-python>=2.0.2
        echo numpy>=1.24.0
        echo pandas>=2.0.0
        echo python-dotenv>=1.0.0
        echo httpx>=0.25.0
        echo websockets>=11.0.0
        echo pydantic>=2.5.0
    ) > requirements.txt
)

echo Installing Python dependencies...
if exist "requirements.txt" (
    pip install -r requirements.txt >nul 2>&1
    if errorlevel 1 (
        echo ⚠️  Some Python dependencies failed to install
        set /a WARNINGS+=1
    ) else (
        echo ✅ Python dependencies installed
    )
) else (
    echo ⚠️  requirements.txt not found, skipping Python dependencies
    set /a WARNINGS+=1
)

echo Installing Node.js dependencies...
if exist "package.json" (
    call npm install >nul 2>&1
    if errorlevel 1 (
        echo ⚠️  Root npm install failed
        set /a WARNINGS+=1
    ) else (
        echo ✅ Root dependencies installed
    )
) else (
    echo ⚠️  Root package.json not found
    set /a WARNINGS+=1
)

echo Installing frontend dependencies...
if exist "frontend\package.json" (
    cd frontend
    call npm install >nul 2>&1
    if errorlevel 1 (
        echo ⚠️  Frontend dependencies failed to install
        set /a WARNINGS+=1
    ) else (
        echo ✅ Frontend dependencies installed
    )
    cd ..
) else (
    echo ⚠️  Frontend package.json not found
    set /a WARNINGS+=1
)

echo Installing backend dependencies...
if exist "backend\package.json" (
    cd backend
    call npm install >nul 2>&1
    if errorlevel 1 (
        echo ⚠️  Backend dependencies failed to install
        set /a WARNINGS+=1
    ) else (
        echo ✅ Backend dependencies installed
    )
    cd ..
) else (
    echo ⚠️  Backend package.json not found, skipping
)

echo Installing blockchain dependencies...
if exist "blockchain\package.json" (
    cd blockchain
    call npm install >nul 2>&1
    if errorlevel 1 (
        echo ⚠️  Blockchain dependencies failed to install
        set /a WARNINGS+=1
    ) else (
        echo ✅ Blockchain dependencies installed
    )
    cd ..
) else (
    echo ⚠️  Blockchain package.json not found, skipping
)

echo.
echo 🐳 Starting Infrastructure Services
echo ===============================================

echo Starting Docker infrastructure...
if exist "docker-compose.enhanced.yml" (
    docker-compose -f docker-compose.enhanced.yml up -d redis kafka zookeeper arangodb >nul 2>&1
    if errorlevel 1 (
        echo ⚠️  Enhanced Docker infrastructure failed to start
        set /a WARNINGS+=1

        :: Try basic docker-compose
        if exist "docker-compose.yml" (
            echo Trying basic Docker infrastructure...
            docker-compose -f docker-compose.yml up -d >nul 2>&1
            if errorlevel 1 (
                echo ❌ Docker infrastructure failed to start
                set /a ERRORS+=1
            ) else (
                echo ✅ Basic Docker infrastructure started
            )
        )
    ) else (
        echo ✅ Enhanced Docker infrastructure started
    )
) else (
    echo ⚠️  docker-compose.enhanced.yml not found
    if exist "docker-compose.yml" (
        echo Starting basic Docker infrastructure...
        docker-compose -f docker-compose.yml up -d >nul 2>&1
        if errorlevel 1 (
            echo ❌ Docker infrastructure failed to start
            set /a ERRORS+=1
        ) else (
            echo ✅ Basic Docker infrastructure started
        )
    ) else (
        echo ❌ No Docker compose files found
        set /a ERRORS+=1
    )
)

echo Waiting for infrastructure to initialize...
timeout /t 15 /nobreak >nul

echo.
echo 🚀 Launching Core Services
echo ===============================================

call :start_docker_service "API Gateway" "start /b \"API Gateway\" docker-compose -f docker-compose.yml up api-gateway"

call :start_docker_service "Kontour Integration" "start /b \"Kontour Integration\" docker-compose -f docker-compose.yml up kontour-integration"

call :start_docker_service "AI Service" "start /b \"AI Service\" docker-compose -f docker-compose.yml up ai-service"

call :start_docker_service "Realtime Service" "start /b \"Realtime Service\" docker-compose -f docker-compose.yml up realtime-service"

call :start_docker_service "Workflow Orchestrator" "start /b \"Workflow Orchestrator\" docker-compose -f docker-compose.yml up workflow-orchestrator"

call :start_docker_service "AGI Orchestrator" "start /b \"AGI Orchestrator\" docker-compose -f docker-compose.yml up agi-orchestrator"

call :start_docker_service "Quantum Utilities" "start /b \"Quantum Utilities\" docker-compose -f docker-compose.yml up quantum-utilities"

call :start_docker_service "Wallet Service" "start /b \"Wallet Service\" docker-compose -f docker-compose.yml up wallet-service"

echo.
echo 🤖 Launching AI and Advanced Services
echo ===============================================

call :start_docker_service "Enhanced Workflow" "start /b \"Enhanced Workflow\" docker-compose -f docker-compose.enhanced.yml up enhanced-workflow-orchestrator"

call :start_python_service "Unified Workflow" "services/workflow-orchestrator/unified_workflow_orchestrator.py"

call :start_python_service "Backend Workflow" "services/backend-workflow-integration/backend_workflow_service.py"

call :start_python_service "Quantum Workflow API" "services/quantum-workflow-api/quantum_workflow_api.py"

call :start_python_service "Chainlink Integration" "services/chainlink-integration/chainlink_service.py"

call :start_python_service "AI Agent LLM Service" "services/ai-agent-llm/ai_agent_service.py"

call :start_python_service "AI Agent Backend Workflow" "services/ai-agent-llm/backend_workflow_integration.py"

call :start_python_service "Complete Backend Workflow" "services/complete-backend-workflow/backend_workflow_service.py"

call :start_npm_service "Blockchain Workflow" "blockchain" "npm run start:complete"

call :start_docker_service "Agentic AI Service" "start /b \"Agentic AI\" docker-compose -f docker-compose.enhanced.yml up agentic-ai-service"

call :start_docker_service "Neural Network Service" "start /b \"Neural Network\" docker-compose -f docker-compose.enhanced.yml up neural-network-service"

call :start_docker_service "BigData Service" "start /b \"BigData Service\" docker-compose -f docker-compose.enhanced.yml up bigdata-service"

call :start_docker_service "IoT Processing Service" "start /b \"IoT Processing\" docker-compose -f docker-compose.enhanced.yml up iot-processing-service"

echo.
echo ⚛️ Launching Quantum & Blockchain Services
echo ===============================================

call :start_python_service "Quantum Computing Service" "services/quantum-computing/quantum_service.py"

call :start_python_service "Quantum Genomics Service" "services/quantum-genomics-service/quantum_genomics_service.py"

call :start_npm_service "Blockchain Network" "blockchain" "npm run start"

call :start_python_service "Web3 AI Workflow" "services/web3-ai-workflow/web3_ai_blockchain_service.py"

call :start_python_service "Enhanced Trading Engine" "services/enhanced-trading-engine/enhanced_trading_engine.py"

echo.
echo 🌐 Launching Frontend Applications
echo ===============================================

echo Building frontend...
if exist "frontend\package.json" (
    cd frontend
    call npm run build >nul 2>&1
    if errorlevel 1 (
        echo ⚠️  Frontend build failed, starting without build
        set /a WARNINGS+=1
    ) else (
        echo ✅ Frontend built successfully
    )

    echo Starting frontend development server...
    start /b "Frontend" npm start >nul 2>&1
    if errorlevel 1 (
        echo ⚠️  Frontend failed to start
        set /a WARNINGS+=1
    ) else (
        echo ✅ Frontend started
    )
    cd ..
) else (
    echo ⚠️  Frontend directory not found
    set /a WARNINGS+=1
)

echo.
echo 📊 Launching Monitoring & Analytics
echo ===============================================

echo Starting Accuracy Monitor...
start /b "Accuracy Monitor" python services/accuracy-monitor/accuracy_monitor.py

echo Starting Analytics Service...
start /b "Analytics Service" python services/analytics-service/analytics_service.py

echo Starting Cybersecurity Service...
start /b "Cybersecurity" python services/cybersecurity-design-service/cybersecurity_service.py

echo.
echo 🔗 Launching Integration Services
echo ===============================================

echo Starting Exchange Integration...
start /b "Exchange Integration" python services/exchange-integration/exchange_integration_service.py

echo Starting LLM Service...
start /b "LLM Service" python services/llm-service/llm_service.py

echo Starting Ingestion Service...
start /b "Ingestion Service" python services/ingestion-service/ingestion_service.py

echo.
echo ⏳ Waiting for All Services to Initialize...
echo ===============================================
timeout /t 60 /nobreak

echo.
echo 🔍 Performing Health Checks
echo ===============================================

:: Check if curl is available
curl --version >nul 2>&1
if errorlevel 1 (
    echo ⚠️  curl not available, skipping detailed health checks
    set /a WARNINGS+=1
    goto :skip_health_checks
)

echo Checking API Gateway...
curl -s --connect-timeout 5 http://localhost:8080/health >nul 2>&1
if errorlevel 1 (
    echo ⚠️  API Gateway health check failed
    set /a WARNINGS+=1
) else (
    echo ✅ API Gateway healthy
)

echo Checking Frontend...
curl -s --connect-timeout 5 http://localhost:3000 >nul 2>&1
if errorlevel 1 (
    echo ⚠️  Frontend health check failed
    set /a WARNINGS+=1
) else (
    echo ✅ Frontend healthy
)

echo Checking Kontour Integration...
curl -s --connect-timeout 5 http://localhost:8010/health >nul 2>&1
if errorlevel 1 (
    echo ⚠️  Kontour Integration health check failed
    set /a WARNINGS+=1
) else (
    echo ✅ Kontour Integration healthy
)

echo Checking AI Service...
curl -s --connect-timeout 5 http://localhost:8020/health >nul 2>&1
if errorlevel 1 (
    echo ⚠️  AI Service health check failed
    set /a WARNINGS+=1
) else (
    echo ✅ AI Service healthy
)

echo Checking Realtime Service...
curl -s --connect-timeout 5 http://localhost:8030/health >nul 2>&1
if errorlevel 1 (
    echo ⚠️  Realtime Service health check failed
    set /a WARNINGS+=1
) else (
    echo ✅ Realtime Service healthy
)

:skip_health_checks

echo.
echo ████████████████████████████████████████████████████████████████
echo ██                                                            ██
echo ██    🎉 KONTOUR COIN ECOSYSTEM SUCCESSFULLY LAUNCHED! 🎉     ██
echo ██                                                            ██
echo ████████████████████████████████████████████████████████████████
echo.

:: Display error summary
if !ERRORS! gtr 0 (
    echo ❌ ERRORS ENCOUNTERED: !ERRORS!
    echo Some critical services may not be running properly.
    echo Please check the logs and ensure all prerequisites are installed.
    echo.
)

if !WARNINGS! gtr 0 (
    echo ⚠️  WARNINGS: !WARNINGS!
    echo Some services may have issues but the ecosystem should still function.
    echo.
)

if !ERRORS! equ 0 if !WARNINGS! equ 0 (
    echo ✅ ALL SERVICES STARTED SUCCESSFULLY!
    echo.
)

echo 🌟 ACCESS POINTS:
echo ===============================================
echo.
echo 📱 FRONTEND APPLICATIONS:
echo   • Main Dashboard:      http://localhost:3000
echo   • Trading Platform:    http://localhost:3000/trading
echo   • Analytics Dashboard: http://localhost:3000/analytics
echo   • AI Laboratory:       http://localhost:3000/ai-lab
echo   • Quantum Dashboard:   http://localhost:3000/quantum
echo   • IoT Dashboard:       http://localhost:3000/iot
echo   • Wallet Interface:    http://localhost:3000/wallet
echo   • Block Explorer:      http://localhost:3000/explorer
echo   • Workflow Manager:    http://localhost:3000/workflow
echo   • Quantum Laboratory:  http://localhost:3000/quantum-workflow
echo   • Chainlink Dashboard: http://localhost:3000/chainlink
echo.

echo 🔧 API ENDPOINTS:
echo   • API Gateway:         http://localhost:8080
echo   • Kontour Integration: http://localhost:8010
echo   • AI Service:          http://localhost:8020
echo   • Realtime Service:    http://localhost:8030
echo   • Wallet Service:      http://localhost:3001
echo   • BigData Service:     http://localhost:8040
echo   • Neural Network:      http://localhost:8050
echo   • IoT Processing:      http://localhost:8060
echo   • Agentic AI:          http://localhost:8070
echo   • Unified Workflow:    http://localhost:8000
echo   • Backend Workflow:    http://localhost:8100
echo   • Quantum Workflow:    http://localhost:8090
echo   • Chainlink Service:   http://localhost:8095
echo   • AI Agent LLM:        http://localhost:8025
echo   • AI Agent WebSocket:  ws://localhost:8026
echo   • Backend Workflow:    http://localhost:8100
echo   • Workflow WebSocket:  ws://localhost:8027
echo   • Blockchain Runner:   blockchain/scripts/run-complete-blockchain-workflow.bat
echo.

echo 🗄️ INFRASTRUCTURE:
echo   • Redis Cache:         localhost:6379
echo   • Kafka Messaging:     localhost:9092
echo   • ArangoDB:           http://localhost:8529
echo   • Ethereum Node:      http://localhost:8545
echo.

echo 📊 MONITORING:
echo   • System Health:      http://localhost:8080/health
echo   • Service Status:     http://localhost:8010/status
echo   • Analytics Metrics:  http://localhost:8040/metrics
echo   • Performance Stats:  http://localhost:8050/stats
echo.

echo 🚀 NEXT STEPS:
echo ===============================================
echo 1. 🦊 Connect MetaMask wallet to http://localhost:8545
echo 2. 💰 Get test KNTOUR tokens from the faucet
echo 3. 📈 Explore AI-powered trading features
echo 4. ⚛️ Monitor quantum security metrics
echo 5. 📊 Access real-time analytics and insights
echo 6. 🤖 Interact with AI agents and workflows
echo 7. 🔗 Test cross-chain functionality
echo 8. 🧬 Explore genomic data features
echo 9. 🌐 Monitor IoT sensor integrations
echo 10. 📱 Test mobile-responsive interfaces
echo.

echo 📖 DOCUMENTATION:
echo   • API Docs:           http://localhost:8080/docs
echo   • User Guide:         https://docs.kontourcoin.com
echo   • Developer Guide:    https://dev.kontourcoin.com
echo   • GitHub Repository:  https://github.com/kontour-coin
echo.

echo 🛑 TO STOP ALL SERVICES:
echo   Run: docker-compose down --remove-orphans
echo   Or:  Ctrl+C in each service window
echo.

:: Display final error summary
echo 📊 LAUNCH SUMMARY:
echo ===============================================
if !ERRORS! gtr 0 (
    echo ❌ CRITICAL ERRORS: !ERRORS!
    echo ⚠️  Some services failed to start properly
    echo 🔧 Please check the troubleshooting section above
) else if !WARNINGS! gtr 0 (
    echo ⚠️  WARNINGS: !WARNINGS!
    echo ✅ Core services are running
    echo 💡 Some optional services may have issues
) else (
    echo ✅ ALL SERVICES LAUNCHED SUCCESSFULLY!
    echo 🎉 No errors or warnings detected
)
echo.

if !ERRORS! equ 0 (
    echo Opening main dashboard...
    timeout /t 3 /nobreak >nul
    start http://localhost:3000
) else (
    echo ⚠️  Dashboard may not be fully functional due to errors
    echo Please resolve the issues above before proceeding
)

echo.
echo ✨ Welcome to the Future of Cryptocurrency! ✨
echo 💎 Kontour Coin - Where AI Meets Blockchain 💎
echo.

if !ERRORS! gtr 0 (
    echo ❌ Launch completed with !ERRORS! error(s) and !WARNINGS! warning(s)
    echo Press any key to continue...
    pause >nul
) else (
    echo ✅ Launch completed successfully!
    pause
)
