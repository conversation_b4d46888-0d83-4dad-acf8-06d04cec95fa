"""
Kontour Coin Quantum Workflow Metrics
Enhanced metrics and performance monitoring for quantum workflows
Integrated with Kontour's analytics and monitoring systems
"""

from dataclasses import dataclass, asdict
from typing import Dict, List, Any, Optional, Union
import numpy as np
import time
import json
from datetime import datetime, timed<PERSON>ta
from enum import Enum

class MetricType(Enum):
    """Types of metrics collected"""
    PERFORMANCE = "performance"
    ACCURACY = "accuracy"
    RESOURCE = "resource"
    SECURITY = "security"
    QUANTUM = "quantum"
    BUSINESS = "business"

class AlertLevel(Enum):
    """Alert levels for metric thresholds"""
    INFO = "info"
    WARNING = "warning"
    CRITICAL = "critical"
    EMERGENCY = "emergency"

@dataclass
class QuantumMetric:
    """Individual quantum metric with metadata"""
    name: str
    value: Union[float, int, str]
    unit: str
    metric_type: MetricType
    timestamp: str
    algorithm_name: str
    workflow_id: str
    metadata: Dict[str, Any]
    alert_level: Optional[AlertLevel] = None
    threshold_breached: bool = False

@dataclass
class PerformanceBenchmark:
    """Performance benchmark data"""
    algorithm_name: str
    execution_time_ms: float
    memory_usage_mb: float
    cpu_utilization: float
    quantum_gates_executed: int
    classical_operations: int
    throughput_ops_per_sec: float
    latency_percentiles: Dict[str, float]  # p50, p95, p99
    error_rate: float
    success_rate: float

@dataclass
class AccuracyMetrics:
    """Accuracy and quality metrics"""
    algorithm_name: str
    fidelity: float
    precision: float
    recall: float
    f1_score: float
    quantum_volume: int
    circuit_depth: int
    gate_error_rate: float
    measurement_error_rate: float
    noise_resilience: float
    entanglement_measure: float

@dataclass
class ResourceUtilization:
    """Resource utilization metrics"""
    algorithm_name: str
    qubits_used: int
    qubits_available: int
    circuit_depth: int
    gate_count: int
    classical_memory_mb: float
    quantum_memory_qubits: float
    execution_time_ms: float
    queue_time_ms: float
    total_time_ms: float
    cost_estimate_usd: float

@dataclass
class SecurityMetrics:
    """Security-related metrics"""
    algorithm_name: str
    quantum_resistance_score: float
    cryptographic_strength: float
    attack_resistance: Dict[str, float]
    security_level: str
    vulnerability_count: int
    threat_level: str
    compliance_score: float
    audit_status: str

@dataclass
class BusinessMetrics:
    """Business and operational metrics"""
    algorithm_name: str
    revenue_impact_usd: float
    cost_savings_usd: float
    efficiency_gain_percent: float
    user_satisfaction_score: float
    market_advantage_score: float
    competitive_edge: float
    roi_percent: float
    time_to_market_days: int

class KontourQuantumMetricsCollector:
    """
    Enhanced metrics collector for Kontour quantum workflows
    Provides comprehensive monitoring, alerting, and analytics
    """

    def __init__(self, redis_client=None, kafka_producer=None):
        self.redis_client = redis_client
        self.kafka_producer = kafka_producer
        
        # Metrics storage
        self.metrics_history: List[QuantumMetric] = []
        self.performance_benchmarks: List[PerformanceBenchmark] = []
        self.accuracy_metrics: List[AccuracyMetrics] = []
        self.resource_metrics: List[ResourceUtilization] = []
        self.security_metrics: List[SecurityMetrics] = []
        self.business_metrics: List[BusinessMetrics] = []
        
        # Alert thresholds
        self.thresholds = {
            "execution_time_ms": {"warning": 5000, "critical": 10000},
            "memory_usage_mb": {"warning": 1000, "critical": 2000},
            "error_rate": {"warning": 0.05, "critical": 0.1},
            "fidelity": {"warning": 0.9, "critical": 0.8},
            "security_score": {"warning": 0.7, "critical": 0.5}
        }
        
        # Aggregation windows
        self.aggregation_windows = {
            "1min": timedelta(minutes=1),
            "5min": timedelta(minutes=5),
            "1hour": timedelta(hours=1),
            "1day": timedelta(days=1)
        }

    def collect_performance_metrics(self, algorithm_name: str, workflow_id: str,
                                  execution_time: float, memory_usage: float,
                                  quantum_gates: int, classical_ops: int,
                                  additional_metrics: Dict[str, Any] = None) -> PerformanceBenchmark:
        """Collect comprehensive performance metrics"""
        
        # Calculate derived metrics
        throughput = quantum_gates / max(execution_time, 0.001)  # ops per second
        cpu_util = min(100.0, (execution_time / 1000) * 50)  # Estimated CPU utilization
        
        # Simulate latency percentiles (in practice, collect from actual measurements)
        base_latency = execution_time
        latency_percentiles = {
            "p50": base_latency * 0.8,
            "p95": base_latency * 1.2,
            "p99": base_latency * 1.5
        }
        
        # Calculate error and success rates
        error_rate = additional_metrics.get("error_rate", 0.01)
        success_rate = 1.0 - error_rate
        
        benchmark = PerformanceBenchmark(
            algorithm_name=algorithm_name,
            execution_time_ms=execution_time,
            memory_usage_mb=memory_usage,
            cpu_utilization=cpu_util,
            quantum_gates_executed=quantum_gates,
            classical_operations=classical_ops,
            throughput_ops_per_sec=throughput,
            latency_percentiles=latency_percentiles,
            error_rate=error_rate,
            success_rate=success_rate
        )
        
        self.performance_benchmarks.append(benchmark)
        
        # Create individual metrics
        metrics = [
            QuantumMetric("execution_time", execution_time, "ms", MetricType.PERFORMANCE,
                         datetime.now().isoformat(), algorithm_name, workflow_id, {}),
            QuantumMetric("memory_usage", memory_usage, "MB", MetricType.RESOURCE,
                         datetime.now().isoformat(), algorithm_name, workflow_id, {}),
            QuantumMetric("throughput", throughput, "ops/sec", MetricType.PERFORMANCE,
                         datetime.now().isoformat(), algorithm_name, workflow_id, {}),
            QuantumMetric("success_rate", success_rate, "ratio", MetricType.ACCURACY,
                         datetime.now().isoformat(), algorithm_name, workflow_id, {})
        ]
        
        # Check thresholds and add alerts
        for metric in metrics:
            self._check_thresholds(metric)
            self.metrics_history.append(metric)
        
        # Emit to external systems
        self._emit_metrics(metrics)
        
        return benchmark

    def collect_accuracy_metrics(self, algorithm_name: str, workflow_id: str,
                                fidelity: float, circuit_depth: int, gate_count: int,
                                additional_metrics: Dict[str, Any] = None) -> AccuracyMetrics:
        """Collect accuracy and quality metrics"""
        
        # Calculate derived accuracy metrics
        precision = additional_metrics.get("precision", 0.95)
        recall = additional_metrics.get("recall", 0.93)
        f1_score = 2 * (precision * recall) / (precision + recall) if (precision + recall) > 0 else 0
        
        # Quantum-specific metrics
        quantum_volume = additional_metrics.get("quantum_volume", circuit_depth ** 2)
        gate_error_rate = additional_metrics.get("gate_error_rate", 0.001)
        measurement_error = additional_metrics.get("measurement_error", 0.01)
        noise_resilience = fidelity * (1 - gate_error_rate)
        entanglement_measure = additional_metrics.get("entanglement", 0.8)
        
        accuracy = AccuracyMetrics(
            algorithm_name=algorithm_name,
            fidelity=fidelity,
            precision=precision,
            recall=recall,
            f1_score=f1_score,
            quantum_volume=quantum_volume,
            circuit_depth=circuit_depth,
            gate_error_rate=gate_error_rate,
            measurement_error_rate=measurement_error,
            noise_resilience=noise_resilience,
            entanglement_measure=entanglement_measure
        )
        
        self.accuracy_metrics.append(accuracy)
        
        # Create individual metrics
        metrics = [
            QuantumMetric("fidelity", fidelity, "ratio", MetricType.QUANTUM,
                         datetime.now().isoformat(), algorithm_name, workflow_id, {}),
            QuantumMetric("f1_score", f1_score, "ratio", MetricType.ACCURACY,
                         datetime.now().isoformat(), algorithm_name, workflow_id, {}),
            QuantumMetric("quantum_volume", quantum_volume, "qv", MetricType.QUANTUM,
                         datetime.now().isoformat(), algorithm_name, workflow_id, {}),
            QuantumMetric("noise_resilience", noise_resilience, "ratio", MetricType.QUANTUM,
                         datetime.now().isoformat(), algorithm_name, workflow_id, {})
        ]
        
        for metric in metrics:
            self._check_thresholds(metric)
            self.metrics_history.append(metric)
        
        self._emit_metrics(metrics)
        
        return accuracy

    def collect_security_metrics(self, algorithm_name: str, workflow_id: str,
                                quantum_resistance: float, crypto_strength: float,
                                attack_resistance: Dict[str, float],
                                additional_metrics: Dict[str, Any] = None) -> SecurityMetrics:
        """Collect security-related metrics"""
        
        # Calculate overall security level
        avg_resistance = np.mean(list(attack_resistance.values()))
        if avg_resistance >= 0.9:
            security_level = "critical"
            threat_level = "low"
        elif avg_resistance >= 0.7:
            security_level = "high"
            threat_level = "medium"
        elif avg_resistance >= 0.5:
            security_level = "medium"
            threat_level = "high"
        else:
            security_level = "low"
            threat_level = "critical"
        
        # Security metrics
        vulnerability_count = additional_metrics.get("vulnerabilities", 0)
        compliance_score = additional_metrics.get("compliance", 0.95)
        audit_status = additional_metrics.get("audit_status", "pending")
        
        security = SecurityMetrics(
            algorithm_name=algorithm_name,
            quantum_resistance_score=quantum_resistance,
            cryptographic_strength=crypto_strength,
            attack_resistance=attack_resistance,
            security_level=security_level,
            vulnerability_count=vulnerability_count,
            threat_level=threat_level,
            compliance_score=compliance_score,
            audit_status=audit_status
        )
        
        self.security_metrics.append(security)
        
        # Create individual metrics
        metrics = [
            QuantumMetric("quantum_resistance", quantum_resistance, "score", MetricType.SECURITY,
                         datetime.now().isoformat(), algorithm_name, workflow_id, {}),
            QuantumMetric("crypto_strength", crypto_strength, "score", MetricType.SECURITY,
                         datetime.now().isoformat(), algorithm_name, workflow_id, {}),
            QuantumMetric("avg_attack_resistance", avg_resistance, "score", MetricType.SECURITY,
                         datetime.now().isoformat(), algorithm_name, workflow_id, {}),
            QuantumMetric("compliance_score", compliance_score, "score", MetricType.SECURITY,
                         datetime.now().isoformat(), algorithm_name, workflow_id, {})
        ]
        
        for metric in metrics:
            self._check_thresholds(metric)
            self.metrics_history.append(metric)
        
        self._emit_metrics(metrics)
        
        return security

    def collect_business_metrics(self, algorithm_name: str, workflow_id: str,
                                revenue_impact: float, cost_savings: float,
                                efficiency_gain: float,
                                additional_metrics: Dict[str, Any] = None) -> BusinessMetrics:
        """Collect business and operational metrics"""
        
        # Business metrics
        user_satisfaction = additional_metrics.get("user_satisfaction", 4.2)  # out of 5
        market_advantage = additional_metrics.get("market_advantage", 0.75)
        competitive_edge = additional_metrics.get("competitive_edge", 0.8)
        time_to_market = additional_metrics.get("time_to_market", 90)  # days
        
        # Calculate ROI
        total_investment = additional_metrics.get("investment", 100000)  # USD
        roi_percent = ((revenue_impact + cost_savings) / total_investment) * 100 if total_investment > 0 else 0
        
        business = BusinessMetrics(
            algorithm_name=algorithm_name,
            revenue_impact_usd=revenue_impact,
            cost_savings_usd=cost_savings,
            efficiency_gain_percent=efficiency_gain,
            user_satisfaction_score=user_satisfaction,
            market_advantage_score=market_advantage,
            competitive_edge=competitive_edge,
            roi_percent=roi_percent,
            time_to_market_days=time_to_market
        )
        
        self.business_metrics.append(business)
        
        # Create individual metrics
        metrics = [
            QuantumMetric("revenue_impact", revenue_impact, "USD", MetricType.BUSINESS,
                         datetime.now().isoformat(), algorithm_name, workflow_id, {}),
            QuantumMetric("cost_savings", cost_savings, "USD", MetricType.BUSINESS,
                         datetime.now().isoformat(), algorithm_name, workflow_id, {}),
            QuantumMetric("roi_percent", roi_percent, "percent", MetricType.BUSINESS,
                         datetime.now().isoformat(), algorithm_name, workflow_id, {}),
            QuantumMetric("efficiency_gain", efficiency_gain, "percent", MetricType.BUSINESS,
                         datetime.now().isoformat(), algorithm_name, workflow_id, {})
        ]
        
        for metric in metrics:
            self.metrics_history.append(metric)
        
        self._emit_metrics(metrics)
        
        return business

    def get_aggregated_metrics(self, window: str = "1hour", 
                              algorithm_name: Optional[str] = None) -> Dict[str, Any]:
        """Get aggregated metrics for specified time window"""
        
        if window not in self.aggregation_windows:
            raise ValueError(f"Invalid window: {window}")
        
        window_delta = self.aggregation_windows[window]
        cutoff_time = datetime.now() - window_delta
        
        # Filter metrics by time window and algorithm
        filtered_metrics = [
            m for m in self.metrics_history
            if datetime.fromisoformat(m.timestamp) >= cutoff_time
            and (algorithm_name is None or m.algorithm_name == algorithm_name)
        ]
        
        if not filtered_metrics:
            return {"window": window, "metrics_count": 0}
        
        # Aggregate by metric type
        aggregated = {}
        for metric_type in MetricType:
            type_metrics = [m for m in filtered_metrics if m.metric_type == metric_type]
            if type_metrics:
                numeric_values = [m.value for m in type_metrics if isinstance(m.value, (int, float))]
                if numeric_values:
                    aggregated[metric_type.value] = {
                        "count": len(type_metrics),
                        "avg": np.mean(numeric_values),
                        "min": np.min(numeric_values),
                        "max": np.max(numeric_values),
                        "std": np.std(numeric_values)
                    }
        
        return {
            "window": window,
            "algorithm_name": algorithm_name,
            "metrics_count": len(filtered_metrics),
            "aggregated": aggregated,
            "timestamp": datetime.now().isoformat()
        }

    def get_alerts(self, level: Optional[AlertLevel] = None) -> List[QuantumMetric]:
        """Get metrics that have breached thresholds"""
        alerts = [m for m in self.metrics_history if m.threshold_breached]
        
        if level:
            alerts = [a for a in alerts if a.alert_level == level]
        
        return sorted(alerts, key=lambda x: x.timestamp, reverse=True)

    def generate_report(self, algorithm_name: Optional[str] = None) -> Dict[str, Any]:
        """Generate comprehensive metrics report"""
        
        # Filter by algorithm if specified
        if algorithm_name:
            perf_metrics = [p for p in self.performance_benchmarks if p.algorithm_name == algorithm_name]
            acc_metrics = [a for a in self.accuracy_metrics if a.algorithm_name == algorithm_name]
            sec_metrics = [s for s in self.security_metrics if s.algorithm_name == algorithm_name]
            bus_metrics = [b for b in self.business_metrics if b.algorithm_name == algorithm_name]
        else:
            perf_metrics = self.performance_benchmarks
            acc_metrics = self.accuracy_metrics
            sec_metrics = self.security_metrics
            bus_metrics = self.business_metrics
        
        report = {
            "report_timestamp": datetime.now().isoformat(),
            "algorithm_name": algorithm_name or "all",
            "summary": {
                "total_executions": len(perf_metrics),
                "avg_execution_time": np.mean([p.execution_time_ms for p in perf_metrics]) if perf_metrics else 0,
                "avg_success_rate": np.mean([p.success_rate for p in perf_metrics]) if perf_metrics else 0,
                "avg_fidelity": np.mean([a.fidelity for a in acc_metrics]) if acc_metrics else 0,
                "avg_security_score": np.mean([s.quantum_resistance_score for s in sec_metrics]) if sec_metrics else 0,
                "total_revenue_impact": sum([b.revenue_impact_usd for b in bus_metrics]),
                "total_cost_savings": sum([b.cost_savings_usd for b in bus_metrics])
            },
            "performance": {
                "benchmarks_count": len(perf_metrics),
                "avg_throughput": np.mean([p.throughput_ops_per_sec for p in perf_metrics]) if perf_metrics else 0,
                "avg_memory_usage": np.mean([p.memory_usage_mb for p in perf_metrics]) if perf_metrics else 0
            },
            "accuracy": {
                "metrics_count": len(acc_metrics),
                "avg_quantum_volume": np.mean([a.quantum_volume for a in acc_metrics]) if acc_metrics else 0,
                "avg_noise_resilience": np.mean([a.noise_resilience for a in acc_metrics]) if acc_metrics else 0
            },
            "security": {
                "assessments_count": len(sec_metrics),
                "security_levels": {level: len([s for s in sec_metrics if s.security_level == level]) 
                                  for level in ["critical", "high", "medium", "low"]}
            },
            "business": {
                "metrics_count": len(bus_metrics),
                "avg_roi": np.mean([b.roi_percent for b in bus_metrics]) if bus_metrics else 0,
                "avg_efficiency_gain": np.mean([b.efficiency_gain_percent for b in bus_metrics]) if bus_metrics else 0
            },
            "alerts": {
                "total_alerts": len(self.get_alerts()),
                "critical_alerts": len(self.get_alerts(AlertLevel.CRITICAL)),
                "warning_alerts": len(self.get_alerts(AlertLevel.WARNING))
            }
        }
        
        return report

    def _check_thresholds(self, metric: QuantumMetric):
        """Check if metric breaches defined thresholds"""
        if not isinstance(metric.value, (int, float)):
            return
        
        metric_key = metric.name
        if metric_key in self.thresholds:
            thresholds = self.thresholds[metric_key]
            
            if metric.value >= thresholds.get("critical", float('inf')):
                metric.alert_level = AlertLevel.CRITICAL
                metric.threshold_breached = True
            elif metric.value >= thresholds.get("warning", float('inf')):
                metric.alert_level = AlertLevel.WARNING
                metric.threshold_breached = True
            elif metric_key == "fidelity" or metric_key == "security_score":
                # For metrics where lower is worse
                if metric.value <= thresholds.get("critical", 0):
                    metric.alert_level = AlertLevel.CRITICAL
                    metric.threshold_breached = True
                elif metric.value <= thresholds.get("warning", 0):
                    metric.alert_level = AlertLevel.WARNING
                    metric.threshold_breached = True

    def _emit_metrics(self, metrics: List[QuantumMetric]):
        """Emit metrics to external systems"""
        if not self.kafka_producer:
            return
        
        for metric in metrics:
            try:
                event = {
                    "event_type": "quantum_metric",
                    "metric": asdict(metric),
                    "timestamp": datetime.now().isoformat()
                }
                self.kafka_producer.send('quantum_metrics', event)
            except Exception as e:
                print(f"Failed to emit metric: {e}")

    def health_check(self) -> Dict[str, Any]:
        """Health check for metrics collector"""
        return {
            "status": "healthy",
            "metrics_collected": len(self.metrics_history),
            "performance_benchmarks": len(self.performance_benchmarks),
            "accuracy_metrics": len(self.accuracy_metrics),
            "security_metrics": len(self.security_metrics),
            "business_metrics": len(self.business_metrics),
            "active_alerts": len(self.get_alerts()),
            "timestamp": datetime.now().isoformat()
        }
