import React, { createContext, useState, useEffect, useContext, ReactNode } from 'react';
import { ethers } from 'ethers';
import { Web3Context } from './Web3Context';

// Contract ABI (partial, just what we need)
const kontourABI = [
  "function balanceOf(address owner) view returns (uint256)",
  "function transfer(address to, uint256 amount) returns (bool)",
  "function name() view returns (string)",
  "function symbol() view returns (string)",
  "function decimals() view returns (uint8)",
  "function totalSupply() view returns (uint256)",
  "event Transfer(address indexed from, address indexed to, uint256 value)"
];

interface KontourContextType {
  kontourBalance: string;
  kontourName: string;
  kontourSymbol: string;
  kontourDecimals: number;
  kontourTotalSupply: string;
  isLoading: boolean;
  error: string | null;
  transferKontour: (to: string, amount: string) => Promise<boolean>;
  refreshBalance: () => Promise<void>;
}

const defaultContext: KontourContextType = {
  kontourBalance: '0',
  kontourName: '',
  kontourSymbol: '',
  kontourDecimals: 18,
  kontourTotalSupply: '0',
  isLoading: false,
  error: null,
  transferKontour: async () => false,
  refreshBalance: async () => {}
};

export const KontourContext = createContext<KontourContextType>(defaultContext);

interface KontourProviderProps {
  children: ReactNode;
}

export const KontourProvider: React.FC<KontourProviderProps> = ({ children }) => {
  const { account, provider, isConnected } = useContext(Web3Context);
  
  const [kontourBalance, setKontourBalance] = useState<string>('0');
  const [kontourName, setKontourName] = useState<string>('');
  const [kontourSymbol, setKontourSymbol] = useState<string>('');
  const [kontourDecimals, setKontourDecimals] = useState<number>(18);
  const [kontourTotalSupply, setKontourTotalSupply] = useState<string>('0');
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  
  // Contract address (would be set in environment variables in a real app)
  const KONTOUR_CONTRACT_ADDRESS = process.env.REACT_APP_KONTOUR_CONTRACT_ADDRESS || '******************************************';
  
  // Initialize contract and fetch token data
  useEffect(() => {
    const initContract = async () => {
      if (!provider || !isConnected) return;
      
      try {
        setIsLoading(true);
        setError(null);
        
        const kontourContract = new ethers.Contract(KONTOUR_CONTRACT_ADDRESS, kontourABI, provider);
        
        // Fetch token metadata
        const name = await kontourContract.name();
        const symbol = await kontourContract.symbol();
        const decimals = await kontourContract.decimals();
        const totalSupply = await kontourContract.totalSupply();
        
        setKontourName(name);
        setKontourSymbol(symbol);
        setKontourDecimals(decimals);
        setKontourTotalSupply(totalSupply.toString());
        
        // Fetch balance if account is connected
        if (account) {
          const balance = await kontourContract.balanceOf(account);
          setKontourBalance(balance.toString());
        }
        
        setIsLoading(false);
      } catch (err) {
        console.error('Error initializing Kontour contract:', err);
        setError('Failed to initialize Kontour token. Please try again later.');
        setIsLoading(false);
      }
    };
    
    initContract();
  }, [provider, account, isConnected, KONTOUR_CONTRACT_ADDRESS]);
  
  // Refresh balance
  const refreshBalance = async () => {
    if (!provider || !account || !isConnected) return;
    
    try {
      setIsLoading(true);
      
      const kontourContract = new ethers.Contract(KONTOUR_CONTRACT_ADDRESS, kontourABI, provider);
      const balance = await kontourContract.balanceOf(account);
      
      setKontourBalance(balance.toString());
      setIsLoading(false);
    } catch (err) {
      console.error('Error refreshing Kontour balance:', err);
      setError('Failed to refresh Kontour balance. Please try again later.');
      setIsLoading(false);
    }
  };
  
  // Transfer Kontour tokens
  const transferKontour = async (to: string, amount: string) => {
    if (!provider || !account || !isConnected) {
      setError('Wallet not connected');
      return false;
    }
    
    try {
      setIsLoading(true);
      setError(null);
      
      // Get signer
      const signer = provider.getSigner();
      
      // Create contract instance with signer
      const kontourContract = new ethers.Contract(KONTOUR_CONTRACT_ADDRESS, kontourABI, signer);
      
      // Convert amount to wei (based on token decimals)
      const amountInSmallestUnit = ethers.utils.parseUnits(amount, kontourDecimals);
      
      // Send transaction
      const tx = await kontourContract.transfer(to, amountInSmallestUnit);
      
      // Wait for transaction to be mined
      await tx.wait();
      
      // Refresh balance
      await refreshBalance();
      
      setIsLoading(false);
      return true;
    } catch (err) {
      console.error('Error transferring Kontour tokens:', err);
      setError('Failed to transfer Kontour tokens. Please try again.');
      setIsLoading(false);
      return false;
    }
  };
  
  const contextValue: KontourContextType = {
    kontourBalance,
    kontourName,
    kontourSymbol,
    kontourDecimals,
    kontourTotalSupply,
    isLoading,
    error,
    transferKontour,
    refreshBalance
  };
  
  return (
    <KontourContext.Provider value={contextValue}>
      {children}
    </KontourContext.Provider>
  );
};

// Custom hook to use the Kontour context
export const useKontour = () => {
  const context = useContext(KontourContext);
  if (!context) {
    throw new Error('useKontour must be used within a KontourProvider');
  }
  return context;
};

export default KontourProvider;