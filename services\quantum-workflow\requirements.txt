# Kontour Coin Quantum Workflow Requirements
# Core quantum workflow dependencies

# Core Python packages
numpy>=1.24.0
scipy>=1.10.0
pandas>=2.0.0

# Quantum computing frameworks
qiskit>=0.45.0
qiskit-aer>=0.13.0
qiskit-ibm-runtime>=0.17.0
cirq>=1.2.0
pennylane>=0.33.0

# Machine learning and optimization
scikit-learn>=1.3.0
tensorflow>=2.15.0
torch>=2.1.0
transformers>=4.36.0

# Web framework and API
fastapi>=0.104.0
uvicorn>=0.24.0
pydantic>=2.5.0
httpx>=0.25.0

# Data processing and storage
redis>=5.0.0
kafka-python>=2.0.2
aiofiles>=23.2.0

# Monitoring and metrics
prometheus-client>=0.19.0
psutil>=5.9.0

# Utilities
python-dotenv>=1.0.0
pyyaml>=6.0.0
click>=8.1.0
rich>=13.7.0
tqdm>=4.66.0

# Development and testing
pytest>=7.4.0
pytest-asyncio>=0.21.0
black>=23.11.0
flake8>=6.1.0
