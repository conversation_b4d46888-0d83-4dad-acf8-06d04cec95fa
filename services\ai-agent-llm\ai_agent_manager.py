"""
Kontour Coin AI Agent Manager
Orchestrates multiple AI agents with different LLM models and specializations
"""

import asyncio
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
import redis
from kafka import KafkaProducer
from fastapi import FastAPI, HTTPException, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
import httpx
import os
from dotenv import load_dotenv

from kontour_ai_agent import (
    KontourAIAgent, AIAgentConfig, AgentMessage, AgentResponse,
    LLMProvider, AgentRole
)

load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class AgentConsensus:
    """Consensus result from multiple agents"""
    consensus_id: str
    query: str
    participating_agents: List[str]
    individual_responses: List[AgentResponse]
    consensus_response: str
    confidence_score: float
    agreement_level: float
    final_recommendations: List[str]
    timestamp: datetime

class KontourAIAgentManager:
    """Manages multiple AI agents with different specializations"""
    
    def __init__(self):
        self.agents: Dict[str, KontourAIAgent] = {}
        self.agent_configs: Dict[str, AIAgentConfig] = {}
        self.consensus_history: List[AgentConsensus] = []
        
        # Platform integration
        try:
            self.redis_client = redis.Redis(host='localhost', port=6379, decode_responses=True)
            self.kafka_producer = KafkaProducer(
                bootstrap_servers=['localhost:9092'],
                value_serializer=lambda x: json.dumps(x, default=str).encode('utf-8')
            )
            self.platform_connected = True
        except Exception as e:
            logger.warning(f"Platform connection failed: {e}")
            self.platform_connected = False
        
        # Initialize default agents
        self._initialize_default_agents()
        
        logger.info("AI Agent Manager initialized successfully")
    
    def _initialize_default_agents(self):
        """Initialize default AI agents with different specializations"""
        
        default_configs = [
            # Trading Advisor with GPT-4
            AIAgentConfig(
                agent_id="trading_advisor_gpt4",
                name="Kontour Trading Advisor",
                role=AgentRole.TRADING_ADVISOR,
                llm_provider=LLMProvider.OPENAI_GPT4,
                model_name="gpt-4",
                temperature=0.3,
                max_tokens=1500,
                system_prompt="Expert cryptocurrency trading advisor"
            ),
            
            # Market Analyst with Claude
            AIAgentConfig(
                agent_id="market_analyst_claude",
                name="Kontour Market Analyst",
                role=AgentRole.MARKET_ANALYST,
                llm_provider=LLMProvider.CLAUDE_3,
                model_name="claude-3-sonnet-20240229",
                temperature=0.4,
                max_tokens=1200,
                system_prompt="Professional cryptocurrency market analyst"
            ),
            
            # Risk Manager with Gemini
            AIAgentConfig(
                agent_id="risk_manager_gemini",
                name="Kontour Risk Manager",
                role=AgentRole.RISK_MANAGER,
                llm_provider=LLMProvider.GEMINI_PRO,
                model_name="gemini-pro",
                temperature=0.2,
                max_tokens=1000,
                system_prompt="Conservative risk management specialist"
            ),
            
            # Portfolio Optimizer with DeepSeek
            AIAgentConfig(
                agent_id="portfolio_optimizer_deepseek",
                name="Kontour Portfolio Optimizer",
                role=AgentRole.PORTFOLIO_OPTIMIZER,
                llm_provider=LLMProvider.DEEPSEEK,
                model_name="deepseek-chat",
                temperature=0.3,
                max_tokens=1200,
                system_prompt="Advanced portfolio optimization specialist"
            ),
            
            # Technical Analyst with Local Model
            AIAgentConfig(
                agent_id="technical_analyst_local",
                name="Kontour Technical Analyst",
                role=AgentRole.TECHNICAL_ANALYST,
                llm_provider=LLMProvider.LOCAL_LLAMA,
                model_name="local-llama",
                temperature=0.4,
                max_tokens=800,
                system_prompt="Technical analysis and chart pattern expert"
            ),
            
            # News Analyst with GPT-3.5
            AIAgentConfig(
                agent_id="news_analyst_gpt35",
                name="Kontour News Analyst",
                role=AgentRole.NEWS_ANALYST,
                llm_provider=LLMProvider.OPENAI_GPT35,
                model_name="gpt-3.5-turbo",
                temperature=0.5,
                max_tokens=1000,
                system_prompt="Cryptocurrency news and sentiment analyst"
            ),
            
            # Sentiment Analyzer with GPT-4
            AIAgentConfig(
                agent_id="sentiment_analyzer_gpt4",
                name="Kontour Sentiment Analyzer",
                role=AgentRole.SENTIMENT_ANALYZER,
                llm_provider=LLMProvider.OPENAI_GPT4,
                model_name="gpt-4",
                temperature=0.6,
                max_tokens=800,
                system_prompt="Market sentiment and psychology analyst"
            ),
            
            # Blockchain Monitor with Claude
            AIAgentConfig(
                agent_id="blockchain_monitor_claude",
                name="Kontour Blockchain Monitor",
                role=AgentRole.BLOCKCHAIN_MONITOR,
                llm_provider=LLMProvider.CLAUDE_3,
                model_name="claude-3-sonnet-20240229",
                temperature=0.3,
                max_tokens=1000,
                system_prompt="Blockchain and on-chain data analyst"
            )
        ]
        
        for config in default_configs:
            try:
                agent = KontourAIAgent(config)
                self.agents[config.agent_id] = agent
                self.agent_configs[config.agent_id] = config
                logger.info(f"Initialized agent: {config.name} ({config.llm_provider.value})")
            except Exception as e:
                logger.error(f"Failed to initialize agent {config.agent_id}: {e}")
    
    async def query_agent(self, agent_id: str, message: AgentMessage) -> AgentResponse:
        """Query a specific agent"""
        if agent_id not in self.agents:
            raise ValueError(f"Agent {agent_id} not found")
        
        agent = self.agents[agent_id]
        response = await agent.process_message(message)
        
        # Emit event to platform
        if self.platform_connected:
            self._emit_event('agent_response', {
                'agent_id': agent_id,
                'message_id': message.message_id,
                'response': asdict(response)
            })
        
        return response
    
    async def query_multiple_agents(self, agent_ids: List[str], message: AgentMessage) -> List[AgentResponse]:
        """Query multiple agents concurrently"""
        tasks = []
        for agent_id in agent_ids:
            if agent_id in self.agents:
                tasks.append(self.query_agent(agent_id, message))
        
        if not tasks:
            raise ValueError("No valid agents found")
        
        responses = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Filter out exceptions
        valid_responses = [r for r in responses if isinstance(r, AgentResponse)]
        
        return valid_responses
    
    async def get_agent_consensus(self, query: str, user_id: str, context: Dict[str, Any] = None) -> AgentConsensus:
        """Get consensus from multiple agents on a query"""
        
        # Create message
        message = AgentMessage(
            message_id=f"msg_{int(datetime.now().timestamp())}",
            agent_id="consensus_query",
            user_id=user_id,
            content=query,
            message_type="consensus_query",
            timestamp=datetime.now(),
            context=context
        )
        
        # Select relevant agents based on query content
        relevant_agents = self._select_relevant_agents(query)
        
        # Query all relevant agents
        responses = await self.query_multiple_agents(relevant_agents, message)
        
        if not responses:
            raise ValueError("No agents provided valid responses")
        
        # Generate consensus
        consensus = self._generate_consensus(query, responses)
        
        # Store consensus
        self.consensus_history.append(consensus)
        
        # Emit event to platform
        if self.platform_connected:
            self._emit_event('agent_consensus', {
                'consensus_id': consensus.consensus_id,
                'query': query,
                'consensus_response': consensus.consensus_response,
                'confidence_score': consensus.confidence_score
            })
        
        return consensus
    
    def _select_relevant_agents(self, query: str) -> List[str]:
        """Select relevant agents based on query content"""
        query_lower = query.lower()
        relevant_agents = []
        
        # Keywords to agent mapping
        agent_keywords = {
            "trading_advisor_gpt4": ["trade", "buy", "sell", "position", "entry", "exit"],
            "market_analyst_claude": ["market", "trend", "analysis", "forecast", "prediction"],
            "risk_manager_gemini": ["risk", "loss", "volatility", "hedge", "protection"],
            "portfolio_optimizer_deepseek": ["portfolio", "allocation", "diversification", "optimize"],
            "technical_analyst_local": ["technical", "chart", "pattern", "indicator", "support", "resistance"],
            "news_analyst_gpt35": ["news", "event", "announcement", "regulation", "policy"],
            "sentiment_analyzer_gpt4": ["sentiment", "fear", "greed", "emotion", "psychology"],
            "blockchain_monitor_claude": ["blockchain", "on-chain", "transaction", "whale", "network"]
        }
        
        # Score agents based on keyword matches
        agent_scores = {}
        for agent_id, keywords in agent_keywords.items():
            if agent_id in self.agents:
                score = sum(1 for keyword in keywords if keyword in query_lower)
                if score > 0:
                    agent_scores[agent_id] = score
        
        # Select top scoring agents (minimum 2, maximum 5)
        sorted_agents = sorted(agent_scores.items(), key=lambda x: x[1], reverse=True)
        relevant_agents = [agent_id for agent_id, score in sorted_agents[:5]]
        
        # Ensure minimum agents
        if len(relevant_agents) < 2:
            # Add default agents
            default_agents = ["trading_advisor_gpt4", "market_analyst_claude"]
            for agent_id in default_agents:
                if agent_id in self.agents and agent_id not in relevant_agents:
                    relevant_agents.append(agent_id)
                    if len(relevant_agents) >= 2:
                        break
        
        return relevant_agents
    
    def _generate_consensus(self, query: str, responses: List[AgentResponse]) -> AgentConsensus:
        """Generate consensus from multiple agent responses"""
        
        # Calculate agreement level
        agreement_level = self._calculate_agreement_level(responses)
        
        # Extract common themes and recommendations
        all_recommendations = []
        for response in responses:
            all_recommendations.extend(response.recommendations)
        
        # Find most common recommendations
        recommendation_counts = {}
        for rec in all_recommendations:
            rec_lower = rec.lower()
            recommendation_counts[rec_lower] = recommendation_counts.get(rec_lower, 0) + 1
        
        # Get top recommendations
        sorted_recs = sorted(recommendation_counts.items(), key=lambda x: x[1], reverse=True)
        final_recommendations = [rec for rec, count in sorted_recs[:5] if count > 1]
        
        # Generate consensus response
        consensus_response = self._synthesize_consensus_response(query, responses, final_recommendations)
        
        # Calculate overall confidence
        avg_confidence = sum(r.confidence for r in responses) / len(responses)
        confidence_score = avg_confidence * agreement_level  # Adjust by agreement level
        
        return AgentConsensus(
            consensus_id=f"consensus_{int(datetime.now().timestamp())}",
            query=query,
            participating_agents=[r.agent_id for r in responses],
            individual_responses=responses,
            consensus_response=consensus_response,
            confidence_score=confidence_score,
            agreement_level=agreement_level,
            final_recommendations=final_recommendations,
            timestamp=datetime.now()
        )
    
    def _calculate_agreement_level(self, responses: List[AgentResponse]) -> float:
        """Calculate agreement level between agent responses"""
        if len(responses) < 2:
            return 1.0
        
        # Simple agreement calculation based on recommendation overlap
        all_recommendations = []
        for response in responses:
            all_recommendations.append(set(rec.lower() for rec in response.recommendations))
        
        # Calculate pairwise agreement
        agreements = []
        for i in range(len(all_recommendations)):
            for j in range(i + 1, len(all_recommendations)):
                set1, set2 = all_recommendations[i], all_recommendations[j]
                if len(set1) == 0 and len(set2) == 0:
                    agreement = 1.0
                elif len(set1) == 0 or len(set2) == 0:
                    agreement = 0.0
                else:
                    intersection = len(set1.intersection(set2))
                    union = len(set1.union(set2))
                    agreement = intersection / union if union > 0 else 0.0
                agreements.append(agreement)
        
        return sum(agreements) / len(agreements) if agreements else 0.0
    
    def _synthesize_consensus_response(self, query: str, responses: List[AgentResponse], recommendations: List[str]) -> str:
        """Synthesize a consensus response from multiple agent responses"""
        
        # Extract key points from each response
        key_points = []
        for response in responses:
            # Get first sentence or key insight
            sentences = response.content.split('.')
            if sentences:
                key_points.append(sentences[0].strip())
        
        # Build consensus response
        consensus_parts = [
            f"Based on analysis from {len(responses)} specialized AI agents, here's the consensus:",
            "",
            "Key Insights:"
        ]
        
        for i, point in enumerate(key_points[:3], 1):
            if point:
                consensus_parts.append(f"{i}. {point}")
        
        if recommendations:
            consensus_parts.extend([
                "",
                "Consensus Recommendations:"
            ])
            for i, rec in enumerate(recommendations[:3], 1):
                consensus_parts.append(f"{i}. {rec.capitalize()}")
        
        # Add confidence note
        avg_confidence = sum(r.confidence for r in responses) / len(responses)
        confidence_text = "high" if avg_confidence > 0.7 else "moderate" if avg_confidence > 0.5 else "low"
        consensus_parts.append(f"\nConsensus confidence: {confidence_text}")
        
        return "\n".join(consensus_parts)
    
    def get_agent_status(self) -> Dict[str, Any]:
        """Get status of all agents"""
        status = {
            "total_agents": len(self.agents),
            "active_agents": len([a for a in self.agents.values() if a.config.enabled]),
            "agents": {}
        }
        
        for agent_id, agent in self.agents.items():
            status["agents"][agent_id] = {
                "name": agent.config.name,
                "role": agent.config.role.value,
                "llm_provider": agent.config.llm_provider.value,
                "enabled": agent.config.enabled,
                "performance": agent.get_performance_metrics()
            }
        
        return status
    
    def get_consensus_history(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Get recent consensus history"""
        recent_consensus = self.consensus_history[-limit:] if self.consensus_history else []
        return [asdict(consensus) for consensus in recent_consensus]
    
    def _emit_event(self, event_type: str, data: Dict[str, Any]):
        """Emit event to platform"""
        if not self.platform_connected:
            return
        
        event = {
            'event_type': event_type,
            'service': 'ai_agent_manager',
            'timestamp': datetime.now().isoformat(),
            'data': data
        }
        
        try:
            # Emit to Kafka
            self.kafka_producer.send('ai_agent_events', event)
            
            # Cache in Redis
            self.redis_client.setex(
                f"ai_agent_event:{event_type}:{int(datetime.now().timestamp())}",
                3600,  # 1 hour TTL
                json.dumps(event, default=str)
            )
        except Exception as e:
            logger.warning(f"Failed to emit event: {e}")
    
    async def add_agent(self, config: AIAgentConfig) -> bool:
        """Add a new agent"""
        try:
            agent = KontourAIAgent(config)
            self.agents[config.agent_id] = agent
            self.agent_configs[config.agent_id] = config
            logger.info(f"Added new agent: {config.name}")
            return True
        except Exception as e:
            logger.error(f"Failed to add agent {config.agent_id}: {e}")
            return False
    
    def remove_agent(self, agent_id: str) -> bool:
        """Remove an agent"""
        if agent_id in self.agents:
            del self.agents[agent_id]
            del self.agent_configs[agent_id]
            logger.info(f"Removed agent: {agent_id}")
            return True
        return False
    
    def enable_agent(self, agent_id: str) -> bool:
        """Enable an agent"""
        if agent_id in self.agents:
            self.agents[agent_id].config.enabled = True
            return True
        return False
    
    def disable_agent(self, agent_id: str) -> bool:
        """Disable an agent"""
        if agent_id in self.agents:
            self.agents[agent_id].config.enabled = False
            return True
        return False
