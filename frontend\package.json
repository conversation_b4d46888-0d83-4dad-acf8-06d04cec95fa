{"name": "kontour-coin-frontend", "version": "1.0.0", "description": "Kontour Coin Frontend - Advanced cryptocurrency trading platform with Web3 wallet and swap functionality", "private": true, "dependencies": {"@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.5.2", "react": "^18.2.0", "react-dom": "^18.2.0", "react-scripts": "5.0.1", "typescript": "^5.3.2", "web-vitals": "^2.1.4", "ethers": "^5.7.2", "web3": "^4.3.0", "@metamask/detect-provider": "^2.0.0", "react-router-dom": "^6.20.1", "@tanstack/react-query": "^4.36.1", "axios": "^1.6.2", "socket.io-client": "^4.7.4", "react-toastify": "^9.1.3", "chart.js": "^4.4.0", "react-chartjs-2": "^5.2.0", "date-fns": "^2.30.0", "framer-motion": "^10.16.16", "@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@mui/material": "^5.15.0", "@mui/icons-material": "^5.15.0", "react-hook-form": "^7.48.2", "yup": "^1.4.0", "@hookform/resolvers": "^3.3.2", "lodash": "^4.17.21", "bignumber.js": "^9.1.2", "react-copy-to-clipboard": "^5.1.0", "qrcode.react": "^3.1.0", "react-helmet-async": "^2.0.4", "react-error-boundary": "^4.0.11", "react-loading-skeleton": "^3.3.1", "react-spinners": "^0.13.8", "recharts": "^2.8.0"}, "devDependencies": {"@types/jest": "^29.5.3", "@types/node": "^20.10.0", "@types/react": "^18.2.39", "@types/react-dom": "^18.2.17", "@types/lodash": "^4.14.202", "@types/react-copy-to-clipboard": "^5.0.7", "eslint": "^8.55.0", "eslint-config-react-app": "^7.0.1", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "prettier": "^3.1.0", "husky": "^8.0.3", "lint-staged": "^15.2.0", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "tailwindcss": "^3.3.6", "webpack-bundle-analyzer": "^4.10.1", "source-map-explorer": "^2.5.3", "cross-env": "^7.0.3", "serve": "^14.2.1", "concurrently": "^8.2.2", "rimraf": "^5.0.5"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "lint": "eslint src --ext .ts,.tsx,.js,.jsx", "lint:fix": "eslint src --ext .ts,.tsx,.js,.jsx --fix", "format": "prettier --write src/**/*.{ts,tsx,js,jsx,json,css,md}", "type-check": "tsc --noEmit", "analyze": "npm run build && npx source-map-explorer 'build/static/js/*.js'", "serve": "serve -s build -l 3000", "dev": "npm start", "preview": "npm run build && npm run serve", "clean": "rimraf build node_modules", "reinstall": "npm run clean && npm install", "update": "npm update", "audit": "npm audit", "audit:fix": "npm audit fix", "outdated": "npm outdated", "size": "npm run build && bundlesize", "coverage": "npm test -- --coverage --watchAll=false", "test:ci": "npm test -- --coverage --watchAll=false --ci", "storybook": "start-storybook -p 6006", "build-storybook": "build-storybook", "chromatic": "npx chromatic --project-token=your-project-token", "deploy": "npm run build && npm run deploy:surge", "deploy:surge": "surge build kontour-coin.surge.sh", "deploy:netlify": "netlify deploy --prod --dir=build", "deploy:vercel": "vercel --prod", "deploy:firebase": "firebase deploy", "deploy:aws": "aws s3 sync build/ s3://kontour-coin-frontend", "deploy:github": "gh-pages -d build", "docker:build": "docker build -t kontour-coin-frontend .", "docker:run": "docker run -p 3000:3000 kontour-coin-frontend", "docker:push": "docker push kontour-coin-frontend"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"], "rules": {"no-console": "warn", "no-debugger": "warn", "prefer-const": "error", "no-var": "error", "no-unused-vars": "warn", "@typescript-eslint/no-unused-vars": "warn", "react-hooks/exhaustive-deps": "warn"}}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "husky": {"hooks": {"pre-commit": "lint-staged", "pre-push": "npm run type-check && npm run test:ci"}}, "lint-staged": {"src/**/*.{ts,tsx,js,jsx}": ["eslint --fix", "prettier --write"], "src/**/*.{json,css,md}": ["prettier --write"]}, "bundlesize": [{"path": "./build/static/js/*.js", "maxSize": "500 kB"}, {"path": "./build/static/css/*.css", "maxSize": "100 kB"}], "jest": {"collectCoverageFrom": ["src/**/*.{ts,tsx}", "!src/**/*.d.ts", "!src/index.tsx", "!src/reportWebVitals.ts"], "coverageThreshold": {"global": {"branches": 70, "functions": 70, "lines": 70, "statements": 70}}}, "proxy": "http://localhost:3002"}