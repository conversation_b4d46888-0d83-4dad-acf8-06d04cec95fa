{"name": "blockchain", "version": "1.0.0", "scripts": {"build": "tsc", "test": "jest", "start": "node dist/run.js", "hardhat:node": "hardhat node", "hardhat:compile": "hardhat compile", "hardhat:run": "hardhat run scripts/run.ts --network localhost", "test:keploy": "jest --coverage --coverageReporters=text --coverageReporters=cobertura --collectCoverageFrom='./**/*.{js,jsx,ts,tsx}'", "workflow:run": "hardhat run scripts/run-kontour-workflow.js", "workflow:run:local": "hardhat run scripts/run-kontour-workflow.js --network localhost", "workflow:run:sepolia": "hardhat run scripts/run-kontour-workflow.js --network sepolia", "workflow:run:mainnet": "hardhat run scripts/run-kontour-workflow.js --network mainnet", "orchestrate": "hardhat run scripts/kontour-blockchain-orchestrator.js", "orchestrate:local": "hardhat run scripts/kontour-blockchain-orchestrator.js --network localhost", "orchestrate:sepolia": "hardhat run scripts/kontour-blockchain-orchestrator.js --network sepolia", "orchestrate:mainnet": "hardhat run scripts/kontour-blockchain-orchestrator.js --network mainnet", "deploy:local": "hardhat deploy --network localhost", "deploy:sepolia": "hardhat deploy --network sepolia", "deploy:mainnet": "hardhat deploy --network mainnet", "start:complete": "npm run hardhat:compile && npm run deploy:local && npm run orchestrate:local"}, "dependencies": {"@openzeppelin/contracts": "^5.0.0", "@chainlink/contracts": "^0.8.0", "axios": "^1.6.0", "ethers": "^6.4.0", "ws": "^8.14.0", "dotenv": "^16.3.0"}, "devDependencies": {"@nomicfoundation/hardhat-toolbox": "^4.0.0", "@nomicfoundation/hardhat-ethers": "^3.0.0", "@nomicfoundation/hardhat-verify": "^2.0.0", "@chainlink/hardhat-chainlink": "^0.0.4", "hardhat-deploy": "^0.11.45", "@types/jest": "^29.5.0", "@types/node": "^18.15.11", "@typescript-eslint/eslint-plugin": "^5.59.0", "@typescript-eslint/parser": "^5.59.0", "chai": "^4.3.7", "eslint": "^8.38.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-prettier": "^4.2.1", "hardhat": "^2.19.0", "jest": "^29.5.0", "prettier": "^2.8.7", "ts-jest": "^29.1.0", "typescript": "^5.3.2"}}