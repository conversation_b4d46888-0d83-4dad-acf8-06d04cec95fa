import React, { useState, useEffect, useCallback } from 'react';
import { useKontour } from '../contexts/KontourContext';
import { useRealtime } from '../contexts/RealtimeContext';

interface WorkflowTask {
  id: string;
  name: string;
  stage: string;
  pattern: string;
  status: 'pending' | 'running' | 'completed' | 'failed' | 'compensating';
  dependencies: string[];
  parameters: Record<string, any>;
  created_at: string;
  started_at?: string;
  completed_at?: string;
  error_message?: string;
  retry_count: number;
  max_retries: number;
}

interface WorkflowInstance {
  id: string;
  name: string;
  description: string;
  tasks: WorkflowTask[];
  current_stage: string;
  status: 'pending' | 'running' | 'completed' | 'failed';
  created_at: string;
  metadata: Record<string, any>;
}

interface WorkflowDefinition {
  name: string;
  description: string;
  stages: Record<string, {
    tasks: Array<{
      name: string;
      pattern?: string;
      dependencies?: string[];
      parameters?: Record<string, any>;
    }>;
  }>;
  metadata?: Record<string, any>;
}

const WORKFLOW_STAGES = [
  'requirements_design',
  'code_infrastructure', 
  'ci_cd_pipeline',
  'microservices_orchestration',
  'ai_agent_integration',
  'quantum_hpc_workflow',
  'monitoring_compliance'
];

const STAGE_LABELS = {
  requirements_design: '📋 Requirements & Design',
  code_infrastructure: '🏗️ Code & Infrastructure',
  ci_cd_pipeline: '🔄 CI/CD Pipeline',
  microservices_orchestration: '⚙️ Microservices Orchestration',
  ai_agent_integration: '🤖 AI Agent Integration',
  quantum_hpc_workflow: '⚛️ Quantum & HPC Workflow',
  monitoring_compliance: '📊 Monitoring & Compliance'
};

const UnifiedWorkflowManager: React.FC = () => {
  const { kontourBalance, kontourName, isLoading: kontourLoading } = useKontour();
  const { isConnected, messages } = useRealtime();
  const [workflows, setWorkflows] = useState<WorkflowInstance[]>([]);
  const [selectedWorkflow, setSelectedWorkflow] = useState<WorkflowInstance | null>(null);
  const [isCreating, setIsCreating] = useState(false);
  const [workflowDefinition, setWorkflowDefinition] = useState<WorkflowDefinition>({
    name: '',
    description: '',
    stages: {}
  });

  // Predefined workflow templates
  const workflowTemplates = {
    'complete_platform_deployment': {
      name: 'Complete Platform Deployment',
      description: 'End-to-end deployment of Kontour Coin platform with all subsystems',
      stages: {
        requirements_design: {
          tasks: [
            { name: 'requirements_analysis', pattern: 'planning' },
            { name: 'architecture_design', pattern: 'planning' },
            { name: 'security_assessment', pattern: 'planning' }
          ]
        },
        code_infrastructure: {
          tasks: [
            { name: 'frontend_scaffold', pattern: 'tool_use', dependencies: ['requirements_analysis'] },
            { name: 'backend_services', pattern: 'tool_use', dependencies: ['architecture_design'] },
            { name: 'smart_contracts', pattern: 'tool_use', dependencies: ['security_assessment'] },
            { name: 'quantum_modules', pattern: 'tool_use' },
            { name: 'ai_agents', pattern: 'tool_use' },
            { name: 'infrastructure_code', pattern: 'tool_use' }
          ]
        },
        ci_cd_pipeline: {
          tasks: [
            { name: 'ci_build', pattern: 'tool_use', dependencies: ['frontend_scaffold', 'backend_services'] },
            { name: 'cd_deploy', pattern: 'tool_use', dependencies: ['ci_build'] },
            { name: 'integration_tests', pattern: 'reflection', dependencies: ['cd_deploy'] }
          ]
        },
        microservices_orchestration: {
          tasks: [
            { name: 'service_discovery', pattern: 'tool_use' },
            { name: 'load_balancing', pattern: 'tool_use' },
            { name: 'saga_orchestration', pattern: 'saga', dependencies: ['service_discovery'] }
          ]
        },
        ai_agent_integration: {
          tasks: [
            { name: 'llm_integration', pattern: 'tool_use' },
            { name: 'agentic_workflows', pattern: 'planning' },
            { name: 'human_approval', pattern: 'human_in_loop', dependencies: ['agentic_workflows'] }
          ]
        },
        quantum_hpc_workflow: {
          tasks: [
            { name: 'quantum_preprocessing', pattern: 'tool_use' },
            { name: 'quantum_execution', pattern: 'tool_use', dependencies: ['quantum_preprocessing'] },
            { name: 'quantum_postprocessing', pattern: 'reflection', dependencies: ['quantum_execution'] }
          ]
        },
        monitoring_compliance: {
          tasks: [
            { name: 'observability_setup', pattern: 'tool_use' },
            { name: 'anomaly_detection', pattern: 'reflection' },
            { name: 'compliance_reporting', pattern: 'tool_use' }
          ]
        }
      },
      metadata: {
        priority: 'high',
        estimated_duration: '4 hours',
        required_approvals: ['human_approval']
      }
    },
    'ai_trading_deployment': {
      name: 'AI Trading System Deployment',
      description: 'Deploy AI-powered trading system with quantum security',
      stages: {
        requirements_design: {
          tasks: [
            { name: 'trading_requirements', pattern: 'planning' },
            { name: 'risk_assessment', pattern: 'planning' }
          ]
        },
        ai_agent_integration: {
          tasks: [
            { name: 'trading_agents', pattern: 'tool_use', dependencies: ['trading_requirements'] },
            { name: 'risk_management', pattern: 'reflection', dependencies: ['risk_assessment'] },
            { name: 'trading_approval', pattern: 'human_in_loop', dependencies: ['trading_agents'] }
          ]
        },
        quantum_hpc_workflow: {
          tasks: [
            { name: 'quantum_security', pattern: 'tool_use' },
            { name: 'quantum_random', pattern: 'tool_use' }
          ]
        },
        monitoring_compliance: {
          tasks: [
            { name: 'trading_monitoring', pattern: 'reflection' },
            { name: 'regulatory_compliance', pattern: 'tool_use' }
          ]
        }
      }
    }
  };

  const fetchWorkflows = useCallback(async () => {
    try {
      const response = await fetch('/api/workflow/workflows');
      if (response.ok) {
        const data = await response.json();
        setWorkflows(data);
      }
    } catch (error) {
      console.error('Error fetching workflows:', error);
    }
  }, []);

  const createWorkflow = async (definition: WorkflowDefinition) => {
    try {
      const response = await fetch('/api/workflow/workflows', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(definition)
      });

      if (response.ok) {
        const result = await response.json();
        await fetchWorkflows();
        setIsCreating(false);
        return result.workflow_id;
      }
    } catch (error) {
      console.error('Error creating workflow:', error);
    }
  };

  const approveTask = async (workflowId: string, taskId: string, approvalData: Record<string, any>) => {
    try {
      const response = await fetch(`/api/workflow/workflows/${workflowId}/tasks/${taskId}/approve`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(approvalData)
      });

      if (response.ok) {
        await fetchWorkflows();
      }
    } catch (error) {
      console.error('Error approving task:', error);
    }
  };

  useEffect(() => {
    fetchWorkflows();
    const interval = setInterval(fetchWorkflows, 5000); // Refresh every 5 seconds
    return () => clearInterval(interval);
  }, [fetchWorkflows]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return '#4ade80';
      case 'running': return '#3b82f6';
      case 'failed': return '#f87171';
      case 'compensating': return '#fbbf24';
      default: return '#6b7280';
    }
  };

  const getStageProgress = (workflow: WorkflowInstance) => {
    const stageIndex = WORKFLOW_STAGES.indexOf(workflow.current_stage);
    return ((stageIndex + 1) / WORKFLOW_STAGES.length) * 100;
  };

  return (
    <div className="unified-workflow-manager">
      <div className="workflow-header">
        <h2>💎 Unified Workflow Orchestrator</h2>
        <button 
          className="create-workflow-btn"
          onClick={() => setIsCreating(true)}
        >
          Create Workflow
        </button>
      </div>

      {isCreating && (
        <div className="workflow-creation-modal">
          <div className="modal-content">
            <h3>Create New Workflow</h3>
            
            <div className="template-selection">
              <h4>Select Template:</h4>
              {Object.entries(workflowTemplates).map(([key, template]) => (
                <button
                  key={key}
                  className="template-btn"
                  onClick={() => {
                    setWorkflowDefinition(template);
                    createWorkflow(template);
                  }}
                >
                  <div className="template-name">{template.name}</div>
                  <div className="template-description">{template.description}</div>
                </button>
              ))}
            </div>

            <div className="modal-actions">
              <button onClick={() => setIsCreating(false)}>Cancel</button>
            </div>
          </div>
        </div>
      )}

      <div className="workflows-grid">
        {workflows.map(workflow => (
          <div key={workflow.id} className="workflow-card">
            <div className="workflow-header">
              <h3>{workflow.name}</h3>
              <span className={`status-badge ${workflow.status}`}>
                {workflow.status}
              </span>
            </div>

            <div className="workflow-progress">
              <div className="progress-bar">
                <div 
                  className="progress-fill"
                  style={{ width: `${getStageProgress(workflow)}%` }}
                />
              </div>
              <div className="current-stage">
                {STAGE_LABELS[workflow.current_stage as keyof typeof STAGE_LABELS]}
              </div>
            </div>

            <div className="workflow-stats">
              <div className="stat">
                <span>Tasks:</span>
                <span>{workflow.tasks.length}</span>
              </div>
              <div className="stat">
                <span>Completed:</span>
                <span>{workflow.tasks.filter(t => t.status === 'completed').length}</span>
              </div>
              <div className="stat">
                <span>Failed:</span>
                <span>{workflow.tasks.filter(t => t.status === 'failed').length}</span>
              </div>
            </div>

            <button 
              className="view-details-btn"
              onClick={() => setSelectedWorkflow(workflow)}
            >
              View Details
            </button>
          </div>
        ))}
      </div>

      {selectedWorkflow && (
        <div className="workflow-details-modal">
          <div className="modal-content large">
            <div className="modal-header">
              <h3>{selectedWorkflow.name}</h3>
              <button onClick={() => setSelectedWorkflow(null)}>×</button>
            </div>

            <div className="workflow-stages">
              {WORKFLOW_STAGES.map(stage => {
                const stageTasks = selectedWorkflow.tasks.filter(t => t.stage === stage);
                const isCurrentStage = selectedWorkflow.current_stage === stage;
                
                return (
                  <div key={stage} className={`stage-section ${isCurrentStage ? 'current' : ''}`}>
                    <h4>{STAGE_LABELS[stage as keyof typeof STAGE_LABELS]}</h4>
                    
                    <div className="stage-tasks">
                      {stageTasks.map(task => (
                        <div key={task.id} className={`task-item ${task.status}`}>
                          <div className="task-header">
                            <span className="task-name">{task.name}</span>
                            <span 
                              className="task-status"
                              style={{ color: getStatusColor(task.status) }}
                            >
                              {task.status}
                            </span>
                          </div>
                          
                          <div className="task-details">
                            <div className="task-pattern">Pattern: {task.pattern}</div>
                            {task.error_message && (
                              <div className="task-error">Error: {task.error_message}</div>
                            )}
                            {task.pattern === 'human_in_loop' && task.status === 'running' && (
                              <button
                                className="approve-btn"
                                onClick={() => approveTask(selectedWorkflow.id, task.id, { approved: true })}
                              >
                                Approve Task
                              </button>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      )}

      <style jsx>{`
        .unified-workflow-manager {
          padding: 20px;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          min-height: 100vh;
          color: white;
        }

        .workflow-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 30px;
        }

        .create-workflow-btn {
          padding: 12px 24px;
          background: rgba(255, 255, 255, 0.2);
          border: none;
          border-radius: 8px;
          color: white;
          cursor: pointer;
          font-weight: bold;
          transition: all 0.3s ease;
        }

        .create-workflow-btn:hover {
          background: rgba(255, 255, 255, 0.3);
          transform: translateY(-2px);
        }

        .workflow-creation-modal, .workflow-details-modal {
          position: fixed;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: rgba(0, 0, 0, 0.8);
          display: flex;
          align-items: center;
          justify-content: center;
          z-index: 1000;
        }

        .modal-content {
          background: rgba(255, 255, 255, 0.1);
          backdrop-filter: blur(10px);
          border-radius: 12px;
          padding: 30px;
          max-width: 600px;
          width: 90%;
          max-height: 80vh;
          overflow-y: auto;
        }

        .modal-content.large {
          max-width: 1000px;
        }

        .template-selection {
          margin: 20px 0;
        }

        .template-btn {
          display: block;
          width: 100%;
          padding: 15px;
          margin: 10px 0;
          background: rgba(255, 255, 255, 0.1);
          border: 1px solid rgba(255, 255, 255, 0.2);
          border-radius: 8px;
          color: white;
          cursor: pointer;
          text-align: left;
          transition: all 0.3s ease;
        }

        .template-btn:hover {
          background: rgba(255, 255, 255, 0.2);
          transform: translateY(-2px);
        }

        .template-name {
          font-weight: bold;
          margin-bottom: 5px;
        }

        .template-description {
          font-size: 0.9rem;
          opacity: 0.8;
        }

        .workflows-grid {
          display: grid;
          grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
          gap: 20px;
        }

        .workflow-card {
          background: rgba(255, 255, 255, 0.1);
          border-radius: 12px;
          padding: 20px;
          backdrop-filter: blur(10px);
          border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .workflow-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 15px;
        }

        .status-badge {
          padding: 4px 12px;
          border-radius: 20px;
          font-size: 0.8rem;
          font-weight: bold;
          text-transform: uppercase;
        }

        .status-badge.completed { background: #4ade80; color: #000; }
        .status-badge.running { background: #3b82f6; }
        .status-badge.failed { background: #f87171; }
        .status-badge.pending { background: #6b7280; }

        .workflow-progress {
          margin: 15px 0;
        }

        .progress-bar {
          height: 8px;
          background: rgba(255, 255, 255, 0.2);
          border-radius: 4px;
          overflow: hidden;
          margin-bottom: 8px;
        }

        .progress-fill {
          height: 100%;
          background: linear-gradient(90deg, #4ade80, #22c55e);
          transition: width 0.3s ease;
        }

        .current-stage {
          font-size: 0.9rem;
          opacity: 0.8;
        }

        .workflow-stats {
          display: grid;
          grid-template-columns: repeat(3, 1fr);
          gap: 10px;
          margin: 15px 0;
        }

        .stat {
          display: flex;
          flex-direction: column;
          align-items: center;
          padding: 10px;
          background: rgba(255, 255, 255, 0.1);
          border-radius: 8px;
        }

        .view-details-btn {
          width: 100%;
          padding: 10px;
          background: rgba(255, 255, 255, 0.2);
          border: none;
          border-radius: 8px;
          color: white;
          cursor: pointer;
          transition: all 0.3s ease;
        }

        .view-details-btn:hover {
          background: rgba(255, 255, 255, 0.3);
        }

        .workflow-stages {
          max-height: 60vh;
          overflow-y: auto;
        }

        .stage-section {
          margin: 20px 0;
          padding: 15px;
          background: rgba(255, 255, 255, 0.05);
          border-radius: 8px;
          border-left: 4px solid rgba(255, 255, 255, 0.3);
        }

        .stage-section.current {
          border-left-color: #4ade80;
          background: rgba(74, 222, 128, 0.1);
        }

        .stage-tasks {
          margin-top: 10px;
        }

        .task-item {
          padding: 10px;
          margin: 8px 0;
          background: rgba(255, 255, 255, 0.1);
          border-radius: 6px;
          border-left: 3px solid #6b7280;
        }

        .task-item.completed { border-left-color: #4ade80; }
        .task-item.running { border-left-color: #3b82f6; }
        .task-item.failed { border-left-color: #f87171; }

        .task-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 5px;
        }

        .task-name {
          font-weight: bold;
        }

        .task-details {
          font-size: 0.9rem;
          opacity: 0.8;
        }

        .task-error {
          color: #f87171;
          margin-top: 5px;
        }

        .approve-btn {
          margin-top: 10px;
          padding: 8px 16px;
          background: #4ade80;
          border: none;
          border-radius: 6px;
          color: #000;
          cursor: pointer;
          font-weight: bold;
        }

        .modal-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 20px;
        }

        .modal-header button {
          background: none;
          border: none;
          color: white;
          font-size: 24px;
          cursor: pointer;
        }
      `}</style>
    </div>
  );
};

export default UnifiedWorkflowManager;
