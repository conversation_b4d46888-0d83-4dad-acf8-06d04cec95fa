@echo off
setlocal enabledelayedexpansion
title Kontour Coin Complete Backend Workflow Service
color 0A

echo.
echo ████████████████████████████████████████████████████████████████
echo ██                                                            ██
echo ██    🔄 KONTOUR COMPLETE BACKEND WORKFLOW SERVICE 🔄         ██
echo ██                                                            ██
echo ██    Advanced Workflow Orchestration System                 ██
echo ██    Trading • AI • Quantum • Security • Compliance        ██
echo ██                                                            ██
echo ████████████████████████████████████████████████████████████████
echo.

:: Set environment variables
set PYTHONPATH=%CD%
set SERVICE_NAME=complete-backend-workflow
set SERVICE_PORT=8100

echo 🔍 Checking Prerequisites...
echo ===============================================

:: Check Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python not found. Please install Python 3.11+
    pause
    exit /b 1
) else (
    for /f "tokens=*" %%i in ('python --version') do set PYTHON_VERSION=%%i
    echo ✅ Python found: !PYTHON_VERSION!
)

:: Check pip
pip --version >nul 2>&1
if errorlevel 1 (
    echo ❌ pip not found
    pause
    exit /b 1
) else (
    echo ✅ pip found
)

echo.
echo 📦 Installing Dependencies...
echo ===============================================

:: Install requirements
if exist "requirements.txt" (
    echo Installing Python packages...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo ❌ Failed to install dependencies
        pause
        exit /b 1
    ) else (
        echo ✅ Dependencies installed successfully
    )
) else (
    echo ⚠️  requirements.txt not found, installing basic packages...
    pip install fastapi uvicorn redis kafka-python httpx websockets numpy pandas python-dotenv
)

echo.
echo 🔧 Environment Configuration...
echo ===============================================

:: Check for .env file
if not exist ".env" (
    echo Creating .env template...
    (
        echo # Kontour Coin Complete Backend Workflow Configuration
        echo.
        echo # Platform Integration
        echo REDIS_HOST=localhost
        echo REDIS_PORT=6379
        echo KAFKA_BOOTSTRAP_SERVERS=localhost:9092
        echo.
        echo # Service Configuration
        echo SERVICE_HOST=0.0.0.0
        echo SERVICE_PORT=8100
        echo LOG_LEVEL=INFO
        echo.
        echo # Service Endpoints
        echo AI_AGENT_URL=http://localhost:8025
        echo TRADING_ENGINE_URL=http://localhost:8085
        echo RISK_MANAGEMENT_URL=http://localhost:8087
        echo PORTFOLIO_SERVICE_URL=http://localhost:8088
        echo BLOCKCHAIN_SERVICE_URL=http://localhost:8089
        echo QUANTUM_SERVICE_URL=http://localhost:8090
        echo COMPLIANCE_SERVICE_URL=http://localhost:8091
        echo MARKET_DATA_URL=http://localhost:8086
        echo GENOMIC_SERVICE_URL=http://localhost:8092
        echo CYBERSECURITY_SERVICE_URL=http://localhost:8093
        echo CHAINLINK_SERVICE_URL=http://localhost:8095
        echo.
        echo # Workflow Configuration
        echo MAX_CONCURRENT_WORKFLOWS=50
        echo WORKFLOW_TIMEOUT=3600
        echo TASK_RETRY_COUNT=3
        echo CLEANUP_INTERVAL=3600
    ) > .env
    echo ✅ .env template created
    echo ⚠️  Please configure service endpoints in .env file
) else (
    echo ✅ .env file found
)

echo.
echo 🚀 Starting Complete Backend Workflow Service...
echo ===============================================

:: Check if port is available
netstat -an | findstr ":8100 " >nul 2>&1
if not errorlevel 1 (
    echo ⚠️  Port 8100 is already in use
    echo Attempting to start on alternative port...
    set SERVICE_PORT=8101
)

echo Service will start on port: !SERVICE_PORT!
echo.

:: Start the service
echo 🔄 Launching Complete Backend Workflow Service...
echo.
echo Service Information:
echo   • Service URL: http://localhost:!SERVICE_PORT!
echo   • Health Check: http://localhost:!SERVICE_PORT!/health
echo   • API Documentation: http://localhost:!SERVICE_PORT!/docs
echo   • WebSocket: ws://localhost:8027
echo.
echo Supported Workflow Types:
echo   • Trading Workflows (Market analysis, AI recommendations, Risk assessment)
echo   • AI Analysis Workflows (Data collection, AI consensus, Validation)
echo   • Quantum Computation (Quantum algorithms, Optimization, Research)
echo   • Compliance Checks (KYC verification, AML screening, Reporting)
echo   • Cybersecurity Scans (Threat detection, Vulnerability assessment)
echo   • Risk Management (Portfolio analysis, Risk scoring, Mitigation)
echo   • Portfolio Optimization (Asset allocation, Rebalancing, Performance)
echo   • Genomic Processing (Data analysis, Privacy compliance, Tokenization)
echo.
echo Workflow Features:
echo   • Multi-step orchestration with dependencies
echo   • Real-time progress monitoring
echo   • Automatic retry and error handling
echo   • Service health monitoring
echo   • WebSocket real-time updates
echo   • Kafka event integration
echo   • Redis caching and persistence
echo.

:: Start the main service
python backend_workflow_service.py

:: If service exits, show status
echo.
if errorlevel 1 (
    echo ❌ Complete Backend Workflow Service exited with error
    echo.
    echo 🔧 Troubleshooting:
    echo   • Check Redis is running on localhost:6379
    echo   • Check Kafka is running on localhost:9092
    echo   • Verify service endpoints in .env file
    echo   • Check Python dependencies are installed
    echo   • Verify port 8100 is available
    echo   • Check logs for specific error messages
    echo.
) else (
    echo ✅ Complete Backend Workflow Service stopped gracefully
)

echo.
echo 📊 Service Status:
echo   • Service Name: !SERVICE_NAME!
echo   • Port: !SERVICE_PORT!
echo   • Status: Stopped
echo.

echo Press any key to exit...
pause >nul
