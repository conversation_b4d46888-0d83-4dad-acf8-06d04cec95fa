"""
Backend Workflow Integration for AI Agent System
Integrates AI agents with Kontour Coin backend workflows and services
"""

import asyncio
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import redis
from kafka import KafkaProducer, KafkaConsumer
import httpx
import websockets
from dataclasses import dataclass, asdict
import os
from dotenv import load_dotenv

from ai_agent_manager import KontourAIAgentManager
from kontour_ai_agent import AgentMessage, AgentRole

load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class WorkflowEvent:
    """Workflow event structure"""
    event_id: str
    event_type: str
    source_service: str
    data: Dict[str, Any]
    timestamp: datetime
    priority: str = "normal"
    requires_ai_analysis: bool = False

@dataclass
class AIAnalysisResult:
    """AI analysis result structure"""
    analysis_id: str
    event_id: str
    agent_consensus: str
    confidence_score: float
    recommendations: List[str]
    risk_assessment: str
    market_impact: str
    timestamp: datetime

class BackendWorkflowIntegration:
    """Integrates AI agents with backend workflows"""
    
    def __init__(self, agent_manager: KontourAIAgentManager):
        self.agent_manager = agent_manager
        self.redis_client = None
        self.kafka_producer = None
        self.kafka_consumer = None
        self.websocket_connections = set()
        self.active_analyses = {}
        
        # Service endpoints
        self.service_endpoints = {
            'trading_engine': 'http://localhost:8085',
            'market_data': 'http://localhost:8086',
            'risk_management': 'http://localhost:8087',
            'portfolio_service': 'http://localhost:8088',
            'blockchain_monitor': 'http://localhost:8089'
        }
        
        self._initialize_connections()
        
    def _initialize_connections(self):
        """Initialize platform connections"""
        try:
            # Redis connection
            self.redis_client = redis.Redis(
                host=os.getenv('REDIS_HOST', 'localhost'),
                port=int(os.getenv('REDIS_PORT', 6379)),
                decode_responses=True
            )
            
            # Kafka producer
            self.kafka_producer = KafkaProducer(
                bootstrap_servers=[os.getenv('KAFKA_BOOTSTRAP_SERVERS', 'localhost:9092')],
                value_serializer=lambda x: json.dumps(x, default=str).encode('utf-8')
            )
            
            # Kafka consumer for workflow events
            self.kafka_consumer = KafkaConsumer(
                'workflow_events',
                'market_events',
                'trading_events',
                'risk_events',
                bootstrap_servers=[os.getenv('KAFKA_BOOTSTRAP_SERVERS', 'localhost:9092')],
                value_deserializer=lambda x: json.loads(x.decode('utf-8')),
                group_id='ai_agent_workflow_group'
            )
            
            logger.info("✅ Backend workflow integration initialized")
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize connections: {e}")
            raise
    
    async def start_workflow_processing(self):
        """Start processing workflow events"""
        logger.info("🚀 Starting workflow event processing...")
        
        # Start event processing tasks
        tasks = [
            asyncio.create_task(self._process_kafka_events()),
            asyncio.create_task(self._monitor_market_conditions()),
            asyncio.create_task(self._periodic_analysis()),
            asyncio.create_task(self._websocket_server())
        ]
        
        try:
            await asyncio.gather(*tasks)
        except Exception as e:
            logger.error(f"Workflow processing error: {e}")
            raise
    
    async def _process_kafka_events(self):
        """Process incoming Kafka events"""
        logger.info("📨 Starting Kafka event processing...")
        
        while True:
            try:
                # Poll for messages
                message_pack = self.kafka_consumer.poll(timeout_ms=1000)
                
                for topic_partition, messages in message_pack.items():
                    for message in messages:
                        event_data = message.value
                        
                        # Create workflow event
                        workflow_event = WorkflowEvent(
                            event_id=event_data.get('event_id', f"evt_{int(datetime.now().timestamp())}"),
                            event_type=event_data.get('event_type', 'unknown'),
                            source_service=event_data.get('source_service', 'unknown'),
                            data=event_data.get('data', {}),
                            timestamp=datetime.fromisoformat(event_data.get('timestamp', datetime.now().isoformat())),
                            priority=event_data.get('priority', 'normal'),
                            requires_ai_analysis=event_data.get('requires_ai_analysis', False)
                        )
                        
                        # Process event
                        await self._handle_workflow_event(workflow_event)
                        
            except Exception as e:
                logger.error(f"Kafka event processing error: {e}")
                await asyncio.sleep(5)  # Wait before retrying
    
    async def _handle_workflow_event(self, event: WorkflowEvent):
        """Handle individual workflow event"""
        logger.info(f"🔄 Processing event: {event.event_type} from {event.source_service}")
        
        try:
            # Determine if AI analysis is needed
            if event.requires_ai_analysis or self._should_analyze_event(event):
                analysis_result = await self._perform_ai_analysis(event)
                
                # Store analysis result
                self.active_analyses[event.event_id] = analysis_result
                
                # Emit analysis result
                await self._emit_analysis_result(analysis_result)
                
                # Take automated actions if needed
                await self._execute_automated_actions(event, analysis_result)
            
            # Update event in Redis
            self.redis_client.setex(
                f"workflow_event:{event.event_id}",
                3600,  # 1 hour TTL
                json.dumps(asdict(event), default=str)
            )
            
        except Exception as e:
            logger.error(f"Error handling workflow event {event.event_id}: {e}")
    
    def _should_analyze_event(self, event: WorkflowEvent) -> bool:
        """Determine if event requires AI analysis"""
        
        # High priority events always get analyzed
        if event.priority == "high":
            return True
        
        # Specific event types that require analysis
        analysis_required_events = [
            'market_volatility_spike',
            'large_transaction_detected',
            'price_anomaly',
            'news_sentiment_change',
            'risk_threshold_breach',
            'portfolio_rebalance_needed',
            'trading_opportunity',
            'regulatory_update'
        ]
        
        return event.event_type in analysis_required_events
    
    async def _perform_ai_analysis(self, event: WorkflowEvent) -> AIAnalysisResult:
        """Perform AI analysis on workflow event"""
        logger.info(f"🧠 Performing AI analysis for event: {event.event_id}")
        
        try:
            # Prepare analysis query based on event type
            query = self._prepare_analysis_query(event)
            
            # Get AI consensus
            consensus = await self.agent_manager.get_agent_consensus(
                query=query,
                user_id="workflow_system",
                context={
                    "event_id": event.event_id,
                    "event_type": event.event_type,
                    "source_service": event.source_service,
                    "event_data": event.data,
                    "timestamp": event.timestamp.isoformat(),
                    "priority": event.priority
                }
            )
            
            # Extract specific insights
            risk_assessment = self._extract_risk_assessment(consensus.consensus_response)
            market_impact = self._extract_market_impact(consensus.consensus_response)
            
            analysis_result = AIAnalysisResult(
                analysis_id=f"analysis_{int(datetime.now().timestamp())}",
                event_id=event.event_id,
                agent_consensus=consensus.consensus_response,
                confidence_score=consensus.confidence_score,
                recommendations=consensus.final_recommendations,
                risk_assessment=risk_assessment,
                market_impact=market_impact,
                timestamp=datetime.now()
            )
            
            logger.info(f"✅ AI analysis completed for event {event.event_id}")
            return analysis_result
            
        except Exception as e:
            logger.error(f"AI analysis failed for event {event.event_id}: {e}")
            raise
    
    def _prepare_analysis_query(self, event: WorkflowEvent) -> str:
        """Prepare analysis query based on event type"""
        
        event_queries = {
            'market_volatility_spike': f"Analyze the market volatility spike: {event.data}. What are the implications and recommended actions?",
            'large_transaction_detected': f"A large transaction was detected: {event.data}. Analyze the potential market impact and trading opportunities.",
            'price_anomaly': f"Price anomaly detected: {event.data}. Assess the situation and provide trading recommendations.",
            'news_sentiment_change': f"News sentiment has changed: {event.data}. Analyze the impact on market conditions.",
            'risk_threshold_breach': f"Risk threshold breached: {event.data}. Provide risk management recommendations.",
            'portfolio_rebalance_needed': f"Portfolio rebalancing needed: {event.data}. Suggest optimal allocation strategy.",
            'trading_opportunity': f"Trading opportunity identified: {event.data}. Analyze and provide entry/exit recommendations.",
            'regulatory_update': f"Regulatory update: {event.data}. Assess compliance and market implications."
        }
        
        return event_queries.get(
            event.event_type,
            f"Analyze this {event.event_type} event: {event.data}. Provide insights and recommendations."
        )
    
    def _extract_risk_assessment(self, consensus_response: str) -> str:
        """Extract risk assessment from consensus response"""
        risk_keywords = ['risk', 'danger', 'threat', 'volatility', 'loss', 'exposure']
        
        sentences = consensus_response.split('.')
        risk_sentences = []
        
        for sentence in sentences:
            if any(keyword in sentence.lower() for keyword in risk_keywords):
                risk_sentences.append(sentence.strip())
        
        if risk_sentences:
            return '. '.join(risk_sentences[:2]) + '.'
        
        return "Risk assessment: Moderate risk level based on current market conditions."
    
    def _extract_market_impact(self, consensus_response: str) -> str:
        """Extract market impact from consensus response"""
        impact_keywords = ['impact', 'effect', 'influence', 'market', 'price', 'trend']
        
        sentences = consensus_response.split('.')
        impact_sentences = []
        
        for sentence in sentences:
            if any(keyword in sentence.lower() for keyword in impact_keywords):
                impact_sentences.append(sentence.strip())
        
        if impact_sentences:
            return '. '.join(impact_sentences[:2]) + '.'
        
        return "Market impact: Moderate impact expected on current market conditions."
    
    async def _emit_analysis_result(self, analysis: AIAnalysisResult):
        """Emit analysis result to platform"""
        try:
            # Emit to Kafka
            analysis_event = {
                'event_type': 'ai_analysis_completed',
                'service': 'ai_agent_workflow',
                'timestamp': analysis.timestamp.isoformat(),
                'data': asdict(analysis)
            }
            
            self.kafka_producer.send('ai_analysis_results', analysis_event)
            
            # Store in Redis
            self.redis_client.setex(
                f"ai_analysis:{analysis.analysis_id}",
                7200,  # 2 hours TTL
                json.dumps(asdict(analysis), default=str)
            )
            
            # Broadcast to WebSocket connections
            await self._broadcast_to_websockets({
                'type': 'ai_analysis_result',
                'data': asdict(analysis)
            })
            
            logger.info(f"📡 Analysis result emitted: {analysis.analysis_id}")
            
        except Exception as e:
            logger.error(f"Failed to emit analysis result: {e}")
    
    async def _execute_automated_actions(self, event: WorkflowEvent, analysis: AIAnalysisResult):
        """Execute automated actions based on AI analysis"""
        
        # Only execute high-confidence recommendations
        if analysis.confidence_score < 0.8:
            logger.info(f"Skipping automated actions for {event.event_id} - low confidence")
            return
        
        try:
            # Parse recommendations for actionable items
            for recommendation in analysis.recommendations:
                await self._execute_recommendation(recommendation, event, analysis)
                
        except Exception as e:
            logger.error(f"Failed to execute automated actions: {e}")
    
    async def _execute_recommendation(self, recommendation: str, event: WorkflowEvent, analysis: AIAnalysisResult):
        """Execute a specific recommendation"""
        rec_lower = recommendation.lower()
        
        try:
            # Trading actions
            if 'buy' in rec_lower or 'purchase' in rec_lower:
                await self._notify_trading_engine('buy_signal', {
                    'recommendation': recommendation,
                    'confidence': analysis.confidence_score,
                    'analysis_id': analysis.analysis_id
                })
            
            elif 'sell' in rec_lower or 'exit' in rec_lower:
                await self._notify_trading_engine('sell_signal', {
                    'recommendation': recommendation,
                    'confidence': analysis.confidence_score,
                    'analysis_id': analysis.analysis_id
                })
            
            # Risk management actions
            elif 'hedge' in rec_lower or 'protect' in rec_lower:
                await self._notify_risk_management('hedge_recommendation', {
                    'recommendation': recommendation,
                    'risk_assessment': analysis.risk_assessment,
                    'analysis_id': analysis.analysis_id
                })
            
            # Portfolio actions
            elif 'rebalance' in rec_lower or 'allocate' in rec_lower:
                await self._notify_portfolio_service('rebalance_recommendation', {
                    'recommendation': recommendation,
                    'confidence': analysis.confidence_score,
                    'analysis_id': analysis.analysis_id
                })
            
        except Exception as e:
            logger.error(f"Failed to execute recommendation '{recommendation}': {e}")
    
    async def _notify_trading_engine(self, action_type: str, data: Dict[str, Any]):
        """Notify trading engine of AI recommendation"""
        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"{self.service_endpoints['trading_engine']}/ai_recommendation",
                    json={
                        'action_type': action_type,
                        'data': data,
                        'timestamp': datetime.now().isoformat()
                    },
                    timeout=10.0
                )
                
                if response.status_code == 200:
                    logger.info(f"✅ Trading engine notified: {action_type}")
                else:
                    logger.warning(f"⚠️ Trading engine notification failed: {response.status_code}")
                    
        except Exception as e:
            logger.error(f"Failed to notify trading engine: {e}")
    
    async def _notify_risk_management(self, action_type: str, data: Dict[str, Any]):
        """Notify risk management service"""
        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"{self.service_endpoints['risk_management']}/ai_recommendation",
                    json={
                        'action_type': action_type,
                        'data': data,
                        'timestamp': datetime.now().isoformat()
                    },
                    timeout=10.0
                )
                
                if response.status_code == 200:
                    logger.info(f"✅ Risk management notified: {action_type}")
                    
        except Exception as e:
            logger.error(f"Failed to notify risk management: {e}")
    
    async def _notify_portfolio_service(self, action_type: str, data: Dict[str, Any]):
        """Notify portfolio service"""
        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"{self.service_endpoints['portfolio_service']}/ai_recommendation",
                    json={
                        'action_type': action_type,
                        'data': data,
                        'timestamp': datetime.now().isoformat()
                    },
                    timeout=10.0
                )
                
                if response.status_code == 200:
                    logger.info(f"✅ Portfolio service notified: {action_type}")
                    
        except Exception as e:
            logger.error(f"Failed to notify portfolio service: {e}")
    
    async def _monitor_market_conditions(self):
        """Monitor market conditions and trigger analysis when needed"""
        logger.info("📊 Starting market condition monitoring...")
        
        while True:
            try:
                # Check market conditions every 30 seconds
                await asyncio.sleep(30)
                
                # Get current market data
                market_data = await self._fetch_market_data()
                
                if market_data:
                    # Analyze for significant changes
                    if self._detect_significant_change(market_data):
                        # Create market event
                        event = WorkflowEvent(
                            event_id=f"market_evt_{int(datetime.now().timestamp())}",
                            event_type="market_condition_change",
                            source_service="market_monitor",
                            data=market_data,
                            timestamp=datetime.now(),
                            priority="normal",
                            requires_ai_analysis=True
                        )
                        
                        await self._handle_workflow_event(event)
                
            except Exception as e:
                logger.error(f"Market monitoring error: {e}")
                await asyncio.sleep(60)  # Wait longer on error
    
    async def _fetch_market_data(self) -> Optional[Dict[str, Any]]:
        """Fetch current market data"""
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(
                    f"{self.service_endpoints['market_data']}/current",
                    timeout=5.0
                )
                
                if response.status_code == 200:
                    return response.json()
                    
        except Exception as e:
            logger.error(f"Failed to fetch market data: {e}")
        
        return None
    
    def _detect_significant_change(self, market_data: Dict[str, Any]) -> bool:
        """Detect significant market changes"""
        # Simple volatility detection
        volatility = market_data.get('volatility', 0)
        volume_change = market_data.get('volume_change_24h', 0)
        price_change = market_data.get('price_change_24h', 0)
        
        # Thresholds for significant changes
        return (
            abs(volatility) > 0.05 or  # 5% volatility
            abs(volume_change) > 0.3 or  # 30% volume change
            abs(price_change) > 0.1  # 10% price change
        )
    
    async def _periodic_analysis(self):
        """Perform periodic analysis of market conditions"""
        logger.info("⏰ Starting periodic analysis...")
        
        while True:
            try:
                # Run analysis every 5 minutes
                await asyncio.sleep(300)
                
                # Get portfolio status
                portfolio_status = await self._get_portfolio_status()
                
                if portfolio_status:
                    # Create periodic analysis event
                    event = WorkflowEvent(
                        event_id=f"periodic_evt_{int(datetime.now().timestamp())}",
                        event_type="periodic_portfolio_analysis",
                        source_service="ai_agent_workflow",
                        data=portfolio_status,
                        timestamp=datetime.now(),
                        priority="low",
                        requires_ai_analysis=True
                    )
                    
                    await self._handle_workflow_event(event)
                
            except Exception as e:
                logger.error(f"Periodic analysis error: {e}")
                await asyncio.sleep(600)  # Wait longer on error
    
    async def _get_portfolio_status(self) -> Optional[Dict[str, Any]]:
        """Get current portfolio status"""
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(
                    f"{self.service_endpoints['portfolio_service']}/status",
                    timeout=5.0
                )
                
                if response.status_code == 200:
                    return response.json()
                    
        except Exception as e:
            logger.error(f"Failed to get portfolio status: {e}")
        
        return None
    
    async def _websocket_server(self):
        """WebSocket server for real-time communication"""
        logger.info("🔌 Starting WebSocket server...")
        
        async def handle_websocket(websocket, path):
            self.websocket_connections.add(websocket)
            try:
                await websocket.wait_closed()
            finally:
                self.websocket_connections.remove(websocket)
        
        try:
            await websockets.serve(handle_websocket, "localhost", 8026)
            logger.info("✅ WebSocket server started on ws://localhost:8026")
            
            # Keep the server running
            await asyncio.Future()  # Run forever
            
        except Exception as e:
            logger.error(f"WebSocket server error: {e}")
    
    async def _broadcast_to_websockets(self, message: Dict[str, Any]):
        """Broadcast message to all WebSocket connections"""
        if not self.websocket_connections:
            return
        
        message_json = json.dumps(message, default=str)
        
        # Send to all connected clients
        disconnected = set()
        for websocket in self.websocket_connections:
            try:
                await websocket.send(message_json)
            except Exception:
                disconnected.add(websocket)
        
        # Remove disconnected clients
        self.websocket_connections -= disconnected
    
    def get_analysis_history(self, limit: int = 50) -> List[Dict[str, Any]]:
        """Get recent analysis history"""
        analyses = []
        
        try:
            # Get analysis keys from Redis
            keys = self.redis_client.keys("ai_analysis:*")
            
            for key in keys[-limit:]:
                analysis_data = self.redis_client.get(key)
                if analysis_data:
                    analyses.append(json.loads(analysis_data))
            
            # Sort by timestamp
            analyses.sort(key=lambda x: x['timestamp'], reverse=True)
            
        except Exception as e:
            logger.error(f"Failed to get analysis history: {e}")
        
        return analyses
    
    def get_active_analyses(self) -> Dict[str, AIAnalysisResult]:
        """Get currently active analyses"""
        return self.active_analyses.copy()
    
    async def manual_analysis(self, query: str, context: Dict[str, Any] = None) -> AIAnalysisResult:
        """Perform manual AI analysis"""
        
        # Create manual event
        event = WorkflowEvent(
            event_id=f"manual_evt_{int(datetime.now().timestamp())}",
            event_type="manual_analysis",
            source_service="user_request",
            data={"query": query, "context": context or {}},
            timestamp=datetime.now(),
            priority="high",
            requires_ai_analysis=True
        )
        
        return await self._perform_ai_analysis(event)

# Main execution
async def main():
    """Main execution function"""
    logger.info("🚀 Starting Kontour AI Agent Backend Workflow Integration...")

    try:
        # Initialize agent manager
        agent_manager = KontourAIAgentManager()

        # Initialize workflow integration
        workflow_integration = BackendWorkflowIntegration(agent_manager)

        # Start workflow processing
        await workflow_integration.start_workflow_processing()

    except KeyboardInterrupt:
        logger.info("👋 Shutting down AI Agent Backend Workflow Integration...")
    except Exception as e:
        logger.error(f"❌ Fatal error: {e}")
        raise

if __name__ == "__main__":
    asyncio.run(main())
