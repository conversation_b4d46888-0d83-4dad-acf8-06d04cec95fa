@echo off
setlocal enabledelayedexpansion
title Kontour Coin Health Check
color 0A

echo.
echo ████████████████████████████████████████████████████████████████
echo ██                                                            ██
echo ██    💎 KONTOUR COIN HEALTH CHECK UTILITY 💎                 ██
echo ██                                                            ██
echo ████████████████████████████████████████████████████████████████
echo.

set HEALTHY=0
set UNHEALTHY=0
set TIMEOUT_SECONDS=5

echo 🔍 Checking Kontour Coin Ecosystem Health...
echo ===============================================
echo.

:: Check if curl is available
curl --version >nul 2>&1
if errorlevel 1 (
    echo ❌ curl not available - cannot perform detailed health checks
    echo Please install curl or use Windows Subsystem for Linux
    goto :summary
)

:: Define services to check
set "services[0]=Frontend:http://localhost:3000"
set "services[1]=API Gateway:http://localhost:8080/health"
set "services[2]=Kontour Integration:http://localhost:8010/health"
set "services[3]=AI Service:http://localhost:8020/health"
set "services[4]=Realtime Service:http://localhost:8030/health"
set "services[5]=Unified Workflow:http://localhost:8000/health"
set "services[6]=Backend Workflow:http://localhost:8100/health"
set "services[7]=Quantum Workflow:http://localhost:8090/health"
set "services[8]=Chainlink Service:http://localhost:8095/health"

:: Check each service
for /L %%i in (0,1,8) do (
    call :check_service "!services[%%i]!"
)

:: Check infrastructure services
echo.
echo 🏗️ Infrastructure Services:
echo ===============================================

call :check_port "Redis" "localhost" "6379"
call :check_port "Kafka" "localhost" "9092"
call :check_port "ArangoDB" "localhost" "8529"
call :check_port "Ethereum Node" "localhost" "8545"

goto :summary

:check_service
set service_info=%~1
for /f "tokens=1,2 delims=:" %%a in ("%service_info%") do (
    set service_name=%%a
    set service_url=%%b:%%c
)

echo Checking %service_name%...
curl -s --connect-timeout %TIMEOUT_SECONDS% --max-time %TIMEOUT_SECONDS% "%service_url%" >nul 2>&1
if errorlevel 1 (
    echo ❌ %service_name% - UNHEALTHY
    set /a UNHEALTHY+=1
) else (
    echo ✅ %service_name% - HEALTHY
    set /a HEALTHY+=1
)
goto :eof

:check_port
set port_name=%~1
set host=%~2
set port=%~3

echo Checking %port_name% on %host%:%port%...
netstat -an | findstr ":%port% " >nul 2>&1
if errorlevel 1 (
    echo ❌ %port_name% - PORT NOT LISTENING
    set /a UNHEALTHY+=1
) else (
    echo ✅ %port_name% - PORT ACTIVE
    set /a HEALTHY+=1
)
goto :eof

:summary
echo.
echo 📊 HEALTH CHECK SUMMARY:
echo ===============================================
echo ✅ Healthy Services: %HEALTHY%
echo ❌ Unhealthy Services: %UNHEALTHY%
echo.

if %UNHEALTHY% equ 0 (
    echo 🎉 ALL SERVICES ARE HEALTHY!
    echo Your Kontour Coin ecosystem is running perfectly.
    echo.
    echo 🌐 Access Points:
    echo   • Main Dashboard: http://localhost:3000
    echo   • API Gateway: http://localhost:8080
    echo   • Health Monitor: http://localhost:8080/health
    echo.
) else (
    echo ⚠️  SOME SERVICES NEED ATTENTION
    echo.
    echo 🔧 Troubleshooting Steps:
    echo 1. Check if all prerequisites are installed
    echo 2. Ensure Docker Desktop is running
    echo 3. Verify no port conflicts exist
    echo 4. Restart the ecosystem: start-kontour-complete-ecosystem.bat
    echo 5. Check individual service logs for errors
    echo.
    echo 📞 Need Help?
    echo   • Check documentation: https://docs.kontourcoin.com
    echo   • GitHub Issues: https://github.com/kontour-coin/issues
    echo   • Community Discord: https://discord.gg/kontourcoin
    echo.
)

echo 💡 TIP: Run this health check periodically to monitor system status
echo.
pause
