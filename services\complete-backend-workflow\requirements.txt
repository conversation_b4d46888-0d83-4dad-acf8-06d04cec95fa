# Kontour Coin Complete Backend Workflow Service Requirements

# Core Framework
fastapi>=0.104.0
uvicorn>=0.24.0
pydantic>=2.5.0

# Platform Integration
redis>=5.0.0
kafka-python>=2.0.2
httpx>=0.25.0
websockets>=11.0.0

# Data Processing
numpy>=1.24.0
pandas>=2.0.0

# Utilities
python-dotenv>=1.0.0
asyncio-mqtt>=0.13.0
aiofiles>=23.2.1
python-multipart>=0.0.6

# Monitoring & Logging
prometheus-client>=0.19.0
structlog>=23.2.0

# Development & Testing
pytest>=7.4.0
pytest-asyncio>=0.21.0
black>=23.9.0
flake8>=6.1.0
mypy>=1.6.0
