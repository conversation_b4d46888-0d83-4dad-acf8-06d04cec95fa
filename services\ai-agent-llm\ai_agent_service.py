"""
Kontour Coin AI Agent Service
FastAPI service providing REST endpoints for AI Agent interactions
"""

import asyncio
import json
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional
from fastapi import FastAPI, HTTPException, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import uvicorn

from ai_agent_manager import KontourAIAgentManager
from kontour_ai_agent import (
    AgentMessage, AIAgentConfig, LLMProvider, AgentRole
)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Pydantic models for API
class ChatRequest(BaseModel):
    message: str
    user_id: str
    agent_id: Optional[str] = None
    context: Optional[Dict[str, Any]] = None

class ConsensusRequest(BaseModel):
    query: str
    user_id: str
    context: Optional[Dict[str, Any]] = None

class AgentConfigRequest(BaseModel):
    agent_id: str
    name: str
    role: str
    llm_provider: str
    model_name: str
    temperature: float = 0.7
    max_tokens: int = 1000
    system_prompt: str = ""
    enabled: bool = True

class HealthResponse(BaseModel):
    status: str
    service: str
    timestamp: str
    total_agents: int
    active_agents: int
    platform_connected: bool

# Initialize FastAPI app
app = FastAPI(
    title="Kontour AI Agent Service",
    description="AI Agent service with multiple LLM models for cryptocurrency platform",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Global agent manager
agent_manager: Optional[KontourAIAgentManager] = None

@app.on_event("startup")
async def startup_event():
    """Initialize AI Agent Manager on startup"""
    global agent_manager
    
    logger.info("Initializing Kontour AI Agent Service...")
    
    try:
        agent_manager = KontourAIAgentManager()
        logger.info("✅ AI Agent Service initialized successfully")
        
    except Exception as e:
        logger.error(f"❌ Failed to initialize AI Agent Service: {e}")
        raise

@app.get("/health", response_model=HealthResponse)
async def health_check():
    """Health check endpoint"""
    global agent_manager
    
    if not agent_manager:
        raise HTTPException(status_code=503, detail="Agent manager not initialized")
    
    status = agent_manager.get_agent_status()
    
    return HealthResponse(
        status="healthy",
        service="ai-agent-service",
        timestamp=datetime.now().isoformat(),
        total_agents=status["total_agents"],
        active_agents=status["active_agents"],
        platform_connected=agent_manager.platform_connected
    )

@app.post("/chat")
async def chat_with_agent(request: ChatRequest):
    """Chat with a specific agent or get consensus from multiple agents"""
    global agent_manager
    
    if not agent_manager:
        raise HTTPException(status_code=503, detail="Agent manager not initialized")
    
    try:
        # Create agent message
        message = AgentMessage(
            message_id=f"msg_{int(datetime.now().timestamp())}",
            agent_id=request.agent_id or "user_query",
            user_id=request.user_id,
            content=request.message,
            message_type="chat",
            timestamp=datetime.now(),
            context=request.context
        )
        
        if request.agent_id:
            # Query specific agent
            response = await agent_manager.query_agent(request.agent_id, message)
            return {
                "type": "single_agent",
                "agent_id": request.agent_id,
                "response": response.content,
                "confidence": response.confidence,
                "recommendations": response.recommendations,
                "reasoning": response.reasoning,
                "processing_time": response.processing_time,
                "model_used": response.model_used,
                "timestamp": response.timestamp.isoformat()
            }
        else:
            # Get consensus from multiple agents
            consensus = await agent_manager.get_agent_consensus(
                request.message, 
                request.user_id, 
                request.context
            )
            return {
                "type": "consensus",
                "consensus_id": consensus.consensus_id,
                "response": consensus.consensus_response,
                "confidence": consensus.confidence_score,
                "agreement_level": consensus.agreement_level,
                "recommendations": consensus.final_recommendations,
                "participating_agents": consensus.participating_agents,
                "individual_responses": [
                    {
                        "agent_id": r.agent_id,
                        "content": r.content,
                        "confidence": r.confidence,
                        "model_used": r.model_used
                    } for r in consensus.individual_responses
                ],
                "timestamp": consensus.timestamp.isoformat()
            }
            
    except Exception as e:
        logger.error(f"Chat request failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/consensus")
async def get_consensus(request: ConsensusRequest):
    """Get consensus from multiple agents"""
    global agent_manager
    
    if not agent_manager:
        raise HTTPException(status_code=503, detail="Agent manager not initialized")
    
    try:
        consensus = await agent_manager.get_agent_consensus(
            request.query,
            request.user_id,
            request.context
        )
        
        return {
            "consensus_id": consensus.consensus_id,
            "query": consensus.query,
            "response": consensus.consensus_response,
            "confidence_score": consensus.confidence_score,
            "agreement_level": consensus.agreement_level,
            "recommendations": consensus.final_recommendations,
            "participating_agents": consensus.participating_agents,
            "individual_responses": [
                {
                    "agent_id": r.agent_id,
                    "content": r.content[:200] + "..." if len(r.content) > 200 else r.content,
                    "confidence": r.confidence,
                    "model_used": r.model_used
                } for r in consensus.individual_responses
            ],
            "timestamp": consensus.timestamp.isoformat()
        }
        
    except Exception as e:
        logger.error(f"Consensus request failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/agents")
async def list_agents():
    """List all available agents"""
    global agent_manager
    
    if not agent_manager:
        raise HTTPException(status_code=503, detail="Agent manager not initialized")
    
    status = agent_manager.get_agent_status()
    return status["agents"]

@app.get("/agents/{agent_id}")
async def get_agent_details(agent_id: str):
    """Get details of a specific agent"""
    global agent_manager
    
    if not agent_manager:
        raise HTTPException(status_code=503, detail="Agent manager not initialized")
    
    if agent_id not in agent_manager.agents:
        raise HTTPException(status_code=404, detail="Agent not found")
    
    agent = agent_manager.agents[agent_id]
    return {
        "agent_id": agent_id,
        "config": {
            "name": agent.config.name,
            "role": agent.config.role.value,
            "llm_provider": agent.config.llm_provider.value,
            "model_name": agent.config.model_name,
            "temperature": agent.config.temperature,
            "max_tokens": agent.config.max_tokens,
            "enabled": agent.config.enabled
        },
        "performance": agent.get_performance_metrics(),
        "conversation_summary": agent.get_conversation_summary()
    }

@app.post("/agents")
async def add_agent(request: AgentConfigRequest):
    """Add a new agent"""
    global agent_manager
    
    if not agent_manager:
        raise HTTPException(status_code=503, detail="Agent manager not initialized")
    
    try:
        # Validate role and provider
        role = AgentRole(request.role)
        provider = LLMProvider(request.llm_provider)
        
        config = AIAgentConfig(
            agent_id=request.agent_id,
            name=request.name,
            role=role,
            llm_provider=provider,
            model_name=request.model_name,
            temperature=request.temperature,
            max_tokens=request.max_tokens,
            system_prompt=request.system_prompt,
            enabled=request.enabled
        )
        
        success = await agent_manager.add_agent(config)
        
        if success:
            return {"status": "success", "message": f"Agent {request.agent_id} added successfully"}
        else:
            raise HTTPException(status_code=500, detail="Failed to add agent")
            
    except ValueError as e:
        raise HTTPException(status_code=400, detail=f"Invalid configuration: {e}")
    except Exception as e:
        logger.error(f"Failed to add agent: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.delete("/agents/{agent_id}")
async def remove_agent(agent_id: str):
    """Remove an agent"""
    global agent_manager
    
    if not agent_manager:
        raise HTTPException(status_code=503, detail="Agent manager not initialized")
    
    success = agent_manager.remove_agent(agent_id)
    
    if success:
        return {"status": "success", "message": f"Agent {agent_id} removed successfully"}
    else:
        raise HTTPException(status_code=404, detail="Agent not found")

@app.post("/agents/{agent_id}/enable")
async def enable_agent(agent_id: str):
    """Enable an agent"""
    global agent_manager
    
    if not agent_manager:
        raise HTTPException(status_code=503, detail="Agent manager not initialized")
    
    success = agent_manager.enable_agent(agent_id)
    
    if success:
        return {"status": "success", "message": f"Agent {agent_id} enabled"}
    else:
        raise HTTPException(status_code=404, detail="Agent not found")

@app.post("/agents/{agent_id}/disable")
async def disable_agent(agent_id: str):
    """Disable an agent"""
    global agent_manager
    
    if not agent_manager:
        raise HTTPException(status_code=503, detail="Agent manager not initialized")
    
    success = agent_manager.disable_agent(agent_id)
    
    if success:
        return {"status": "success", "message": f"Agent {agent_id} disabled"}
    else:
        raise HTTPException(status_code=404, detail="Agent not found")

@app.get("/consensus/history")
async def get_consensus_history(limit: int = 10):
    """Get consensus history"""
    global agent_manager
    
    if not agent_manager:
        raise HTTPException(status_code=503, detail="Agent manager not initialized")
    
    return agent_manager.get_consensus_history(limit)

@app.get("/models")
async def list_available_models():
    """List available LLM models and providers"""
    return {
        "providers": {
            "openai_gpt4": {
                "name": "OpenAI GPT-4",
                "models": ["gpt-4", "gpt-4-turbo-preview"],
                "description": "Most capable model for complex reasoning"
            },
            "openai_gpt35": {
                "name": "OpenAI GPT-3.5",
                "models": ["gpt-3.5-turbo", "gpt-3.5-turbo-16k"],
                "description": "Fast and efficient for most tasks"
            },
            "claude_3": {
                "name": "Anthropic Claude 3",
                "models": ["claude-3-sonnet-20240229", "claude-3-opus-20240229"],
                "description": "Excellent for analysis and reasoning"
            },
            "gemini_pro": {
                "name": "Google Gemini Pro",
                "models": ["gemini-pro", "gemini-pro-vision"],
                "description": "Google's advanced multimodal model"
            },
            "deepseek": {
                "name": "DeepSeek",
                "models": ["deepseek-chat", "deepseek-coder"],
                "description": "Specialized for coding and technical tasks"
            },
            "local_llama": {
                "name": "Local LLaMA",
                "models": ["local-llama", "local-mistral"],
                "description": "Privacy-focused local models"
            }
        },
        "roles": [role.value for role in AgentRole]
    }

@app.get("/performance")
async def get_performance_metrics():
    """Get overall performance metrics"""
    global agent_manager
    
    if not agent_manager:
        raise HTTPException(status_code=503, detail="Agent manager not initialized")
    
    status = agent_manager.get_agent_status()
    
    # Calculate aggregate metrics
    total_requests = sum(
        agent["performance"]["total_requests"] 
        for agent in status["agents"].values()
    )
    
    total_successful = sum(
        agent["performance"]["successful_responses"] 
        for agent in status["agents"].values()
    )
    
    avg_response_time = 0
    if status["active_agents"] > 0:
        avg_response_time = sum(
            agent["performance"]["average_response_time"] 
            for agent in status["agents"].values() 
            if agent["enabled"]
        ) / status["active_agents"]
    
    return {
        "total_agents": status["total_agents"],
        "active_agents": status["active_agents"],
        "total_requests": total_requests,
        "total_successful_responses": total_successful,
        "overall_success_rate": total_successful / total_requests if total_requests > 0 else 0,
        "average_response_time": avg_response_time,
        "consensus_count": len(agent_manager.consensus_history),
        "platform_connected": agent_manager.platform_connected,
        "timestamp": datetime.now().isoformat()
    }

@app.post("/demo")
async def run_demo():
    """Run a demonstration of AI agent capabilities"""
    global agent_manager
    
    if not agent_manager:
        raise HTTPException(status_code=503, detail="Agent manager not initialized")
    
    try:
        demo_queries = [
            "What's the current market sentiment for Bitcoin?",
            "Should I buy Ethereum now or wait?",
            "What are the risks of investing in altcoins?",
            "How should I diversify my crypto portfolio?"
        ]
        
        demo_results = []
        
        for query in demo_queries:
            consensus = await agent_manager.get_agent_consensus(
                query, 
                "demo_user",
                {"demo": True, "timestamp": datetime.now().isoformat()}
            )
            
            demo_results.append({
                "query": query,
                "response": consensus.consensus_response,
                "confidence": consensus.confidence_score,
                "participating_agents": len(consensus.participating_agents),
                "agreement_level": consensus.agreement_level
            })
            
            # Small delay between queries
            await asyncio.sleep(1)
        
        return {
            "status": "completed",
            "demo_results": demo_results,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Demo failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8025, log_level="info")
