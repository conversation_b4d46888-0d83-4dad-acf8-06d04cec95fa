import React, { useState, useEffect } from 'react';
import {
  Box,
  AppBar,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>graphy,
  IconButton,
  Drawer,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Avatar,
  Menu,
  MenuItem,
  Badge,
  Chip,
  Divider,
  useTheme,
  useMediaQuery,
  Switch,
  FormControlLabel,
  Tooltip,
  Button,
  Card,
  CardContent,
  Grid
} from '@mui/material';
import {
  Menu as MenuIcon,
  Dashboard as DashboardIcon,
  TrendingUp as TradingIcon,
  AccountBalanceWallet as WalletIcon,
  Psychology as AIIcon,
  Science as QuantumIcon,
  Security as SecurityIcon,
  Analytics as AnalyticsIcon,
  Settings as SettingsIcon,
  Notifications as NotificationsIcon,
  AccountCircle as AccountIcon,
  Brightness4 as DarkModeIcon,
  Brightness7 as LightModeIcon,
  Timeline as WorkflowIcon,
  Build as BuilderIcon,
  Assessment as ReportsIcon,
  Support as SupportIcon,
  ExitToApp as LogoutIcon,
  Language as LanguageIcon,
  Gavel as ComplianceIcon,
  Biotech as GenomicsIcon
} from '@mui/icons-material';
import { styled, alpha } from '@mui/material/styles';
import { useLocation, useNavigate } from 'react-router-dom';

const DRAWER_WIDTH = 280;

// Styled Components
const StyledAppBar = styled(AppBar)(({ theme }) => ({
  zIndex: theme.zIndex.drawer + 1,
  backgroundColor: alpha(theme.palette.background.paper, 0.8),
  backdropFilter: 'blur(20px)',
  borderBottom: `1px solid ${alpha(theme.palette.divider, 0.12)}`,
  boxShadow: 'none',
  color: theme.palette.text.primary,
}));

const StyledDrawer = styled(Drawer)(({ theme }) => ({
  width: DRAWER_WIDTH,
  flexShrink: 0,
  '& .MuiDrawer-paper': {
    width: DRAWER_WIDTH,
    boxSizing: 'border-box',
    backgroundColor: theme.palette.background.paper,
    borderRight: `1px solid ${alpha(theme.palette.divider, 0.12)}`,
  },
}));

const LogoContainer = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  padding: theme.spacing(2),
  background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.primary.dark} 100%)`,
  color: theme.palette.primary.contrastText,
  margin: theme.spacing(1),
  borderRadius: theme.shape.borderRadius,
}));

const NavSection = styled(Box)(({ theme }) => ({
  padding: theme.spacing(1, 2),
  marginTop: theme.spacing(1),
}));

const SectionTitle = styled(Typography)(({ theme }) => ({
  fontSize: '0.75rem',
  fontWeight: 600,
  textTransform: 'uppercase',
  letterSpacing: '0.08em',
  color: theme.palette.text.secondary,
  marginBottom: theme.spacing(1),
  marginLeft: theme.spacing(2),
}));

const StyledListItemButton = styled(ListItemButton)(({ theme }) => ({
  borderRadius: theme.shape.borderRadius,
  margin: theme.spacing(0.5, 0),
  '&:hover': {
    backgroundColor: alpha(theme.palette.primary.main, 0.08),
  },
  '&.Mui-selected': {
    backgroundColor: alpha(theme.palette.primary.main, 0.12),
    borderLeft: `3px solid ${theme.palette.primary.main}`,
    '&:hover': {
      backgroundColor: alpha(theme.palette.primary.main, 0.16),
    },
  },
}));

const StatusIndicator = styled(Box)<{ status: 'online' | 'offline' | 'busy' }>(({ theme, status }) => ({
  width: 8,
  height: 8,
  borderRadius: '50%',
  backgroundColor: status === 'online' ? theme.palette.success.main : 
                   status === 'busy' ? theme.palette.warning.main : 
                   theme.palette.error.main,
  marginLeft: 'auto',
}));

const MainContent = styled(Box)(({ theme }) => ({
  flexGrow: 1,
  padding: theme.spacing(3),
  marginLeft: DRAWER_WIDTH,
  minHeight: '100vh',
  backgroundColor: theme.palette.background.default,
  [theme.breakpoints.down('lg')]: {
    marginLeft: 0,
  },
}));

interface NavigationItem {
  id: string;
  label: string;
  icon: React.ReactNode;
  path: string;
  badge?: number;
  status?: 'online' | 'offline' | 'busy';
  children?: NavigationItem[];
}

const navigationItems: NavigationItem[] = [
  {
    id: 'dashboard',
    label: 'Dashboard',
    icon: <DashboardIcon />,
    path: '/dashboard',
  },
  {
    id: 'trading',
    label: 'Trading',
    icon: <TradingIcon />,
    path: '/trading',
    badge: 3,
  },
  {
    id: 'wallet',
    label: 'Wallet',
    icon: <WalletIcon />,
    path: '/wallet',
  },
  {
    id: 'ai-agents',
    label: 'AI Agents',
    icon: <AIIcon />,
    path: '/ai-agents',
    status: 'online',
  },
  {
    id: 'workflows',
    label: 'Workflows',
    icon: <WorkflowIcon />,
    path: '/workflows',
    children: [
      {
        id: 'workflow-dashboard',
        label: 'Dashboard',
        icon: <DashboardIcon />,
        path: '/workflows/dashboard',
      },
      {
        id: 'workflow-builder',
        label: 'Builder',
        icon: <BuilderIcon />,
        path: '/workflows/builder',
      },
    ],
  },
  {
    id: 'quantum',
    label: 'Quantum Lab',
    icon: <QuantumIcon />,
    path: '/quantum',
    status: 'online',
  },
  {
    id: 'analytics',
    label: 'Analytics',
    icon: <AnalyticsIcon />,
    path: '/analytics',
  },
  {
    id: 'compliance',
    label: 'Compliance',
    icon: <ComplianceIcon />,
    path: '/compliance',
  },
  {
    id: 'genomics',
    label: 'Genomics',
    icon: <GenomicsIcon />,
    path: '/genomics',
  },
  {
    id: 'security',
    label: 'Security',
    icon: <SecurityIcon />,
    path: '/security',
    status: 'online',
  },
  {
    id: 'reports',
    label: 'Reports',
    icon: <ReportsIcon />,
    path: '/reports',
  },
];

interface ProfessionalLayoutProps {
  children: React.ReactNode;
  darkMode: boolean;
  onToggleDarkMode: () => void;
}

const ProfessionalLayout: React.FC<ProfessionalLayoutProps> = ({
  children,
  darkMode,
  onToggleDarkMode,
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('lg'));
  const location = useLocation();
  const navigate = useNavigate();
  
  const [mobileOpen, setMobileOpen] = useState(false);
  const [accountMenuAnchor, setAccountMenuAnchor] = useState<null | HTMLElement>(null);
  const [notificationMenuAnchor, setNotificationMenuAnchor] = useState<null | HTMLElement>(null);
  const [notifications] = useState([
    { id: 1, title: 'Trading Alert', message: 'BTC price reached target', time: '2 min ago', type: 'success' },
    { id: 2, title: 'AI Analysis Complete', message: 'Market analysis finished', time: '5 min ago', type: 'info' },
    { id: 3, title: 'Security Scan', message: 'Weekly security scan completed', time: '1 hour ago', type: 'warning' },
  ]);

  const handleDrawerToggle = () => {
    setMobileOpen(!mobileOpen);
  };

  const handleAccountMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setAccountMenuAnchor(event.currentTarget);
  };

  const handleAccountMenuClose = () => {
    setAccountMenuAnchor(null);
  };

  const handleNotificationMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setNotificationMenuAnchor(event.currentTarget);
  };

  const handleNotificationMenuClose = () => {
    setNotificationMenuAnchor(null);
  };

  const handleNavigation = (path: string) => {
    navigate(path);
    if (isMobile) {
      setMobileOpen(false);
    }
  };

  const renderNavigationItems = (items: NavigationItem[], level = 0) => {
    return items.map((item) => (
      <React.Fragment key={item.id}>
        <StyledListItemButton
          selected={location.pathname === item.path}
          onClick={() => handleNavigation(item.path)}
          sx={{ pl: 2 + level * 2 }}
        >
          <ListItemIcon sx={{ color: 'inherit', minWidth: 40 }}>
            {item.icon}
          </ListItemIcon>
          <ListItemText 
            primary={item.label}
            primaryTypographyProps={{
              fontSize: '0.875rem',
              fontWeight: location.pathname === item.path ? 600 : 400,
            }}
          />
          {item.badge && (
            <Badge badgeContent={item.badge} color="error" />
          )}
          {item.status && (
            <StatusIndicator status={item.status} />
          )}
        </StyledListItemButton>
        {item.children && renderNavigationItems(item.children, level + 1)}
      </React.Fragment>
    ));
  };

  const drawerContent = (
    <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* Logo */}
      <LogoContainer>
        <Typography variant="h6" sx={{ fontWeight: 700, display: 'flex', alignItems: 'center', gap: 1 }}>
          💎 Kontour Coin
        </Typography>
      </LogoContainer>

      {/* User Profile */}
      <Box sx={{ p: 2 }}>
        <Card sx={{ background: alpha(theme.palette.primary.main, 0.08) }}>
          <CardContent sx={{ p: 2, '&:last-child': { pb: 2 } }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              <Avatar sx={{ width: 40, height: 40, bgcolor: theme.palette.primary.main }}>
                U
              </Avatar>
              <Box sx={{ flex: 1 }}>
                <Typography variant="subtitle2" fontWeight={600}>
                  User Account
                </Typography>
                <Typography variant="caption" color="text.secondary">
                  Premium Trader
                </Typography>
              </Box>
              <Chip label="Pro" size="small" color="primary" />
            </Box>
          </CardContent>
        </Card>
      </Box>

      {/* Navigation */}
      <Box sx={{ flex: 1, overflow: 'auto' }}>
        <NavSection>
          <SectionTitle>Main</SectionTitle>
          <List disablePadding>
            {renderNavigationItems(navigationItems.slice(0, 4))}
          </List>
        </NavSection>

        <NavSection>
          <SectionTitle>Advanced</SectionTitle>
          <List disablePadding>
            {renderNavigationItems(navigationItems.slice(4, 8))}
          </List>
        </NavSection>

        <NavSection>
          <SectionTitle>Tools</SectionTitle>
          <List disablePadding>
            {renderNavigationItems(navigationItems.slice(8))}
          </List>
        </NavSection>
      </Box>

      {/* Settings */}
      <Box sx={{ p: 2, borderTop: `1px solid ${alpha(theme.palette.divider, 0.12)}` }}>
        <FormControlLabel
          control={
            <Switch
              checked={darkMode}
              onChange={onToggleDarkMode}
              icon={<LightModeIcon />}
              checkedIcon={<DarkModeIcon />}
            />
          }
          label="Dark Mode"
          sx={{ mb: 1 }}
        />
        <StyledListItemButton onClick={() => handleNavigation('/settings')}>
          <ListItemIcon sx={{ color: 'inherit', minWidth: 40 }}>
            <SettingsIcon />
          </ListItemIcon>
          <ListItemText primary="Settings" />
        </StyledListItemButton>
      </Box>
    </Box>
  );

  return (
    <Box sx={{ display: 'flex' }}>
      {/* App Bar */}
      <StyledAppBar position="fixed">
        <Toolbar>
          {isMobile && (
            <IconButton
              color="inherit"
              aria-label="open drawer"
              edge="start"
              onClick={handleDrawerToggle}
              sx={{ mr: 2 }}
            >
              <MenuIcon />
            </IconButton>
          )}
          
          <Typography variant="h6" noWrap component="div" sx={{ flexGrow: 1, fontWeight: 600 }}>
            Kontour Coin Platform
          </Typography>

          {/* Market Status */}
          <Chip
            label="Markets Open"
            color="success"
            size="small"
            sx={{ mr: 2 }}
          />

          {/* Notifications */}
          <Tooltip title="Notifications">
            <IconButton color="inherit" onClick={handleNotificationMenuOpen}>
              <Badge badgeContent={notifications.length} color="error">
                <NotificationsIcon />
              </Badge>
            </IconButton>
          </Tooltip>

          {/* Account Menu */}
          <Tooltip title="Account">
            <IconButton color="inherit" onClick={handleAccountMenuOpen}>
              <AccountIcon />
            </IconButton>
          </Tooltip>
        </Toolbar>
      </StyledAppBar>

      {/* Navigation Drawer */}
      <Box component="nav">
        {isMobile ? (
          <Drawer
            variant="temporary"
            open={mobileOpen}
            onClose={handleDrawerToggle}
            ModalProps={{ keepMounted: true }}
            sx={{
              '& .MuiDrawer-paper': { boxSizing: 'border-box', width: DRAWER_WIDTH },
            }}
          >
            {drawerContent}
          </Drawer>
        ) : (
          <StyledDrawer variant="permanent" open>
            {drawerContent}
          </StyledDrawer>
        )}
      </Box>

      {/* Main Content */}
      <MainContent component="main">
        <Toolbar />
        {children}
      </MainContent>

      {/* Account Menu */}
      <Menu
        anchorEl={accountMenuAnchor}
        open={Boolean(accountMenuAnchor)}
        onClose={handleAccountMenuClose}
        transformOrigin={{ horizontal: 'right', vertical: 'top' }}
        anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
      >
        <MenuItem onClick={handleAccountMenuClose}>
          <ListItemIcon><AccountIcon /></ListItemIcon>
          Profile
        </MenuItem>
        <MenuItem onClick={handleAccountMenuClose}>
          <ListItemIcon><SettingsIcon /></ListItemIcon>
          Settings
        </MenuItem>
        <MenuItem onClick={handleAccountMenuClose}>
          <ListItemIcon><SupportIcon /></ListItemIcon>
          Support
        </MenuItem>
        <Divider />
        <MenuItem onClick={handleAccountMenuClose}>
          <ListItemIcon><LogoutIcon /></ListItemIcon>
          Logout
        </MenuItem>
      </Menu>

      {/* Notifications Menu */}
      <Menu
        anchorEl={notificationMenuAnchor}
        open={Boolean(notificationMenuAnchor)}
        onClose={handleNotificationMenuClose}
        transformOrigin={{ horizontal: 'right', vertical: 'top' }}
        anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
        PaperProps={{ sx: { width: 320, maxHeight: 400 } }}
      >
        <Box sx={{ p: 2, borderBottom: `1px solid ${alpha(theme.palette.divider, 0.12)}` }}>
          <Typography variant="h6" fontWeight={600}>
            Notifications
          </Typography>
        </Box>
        {notifications.map((notification) => (
          <MenuItem key={notification.id} onClick={handleNotificationMenuClose}>
            <Box sx={{ width: '100%' }}>
              <Typography variant="subtitle2" fontWeight={600}>
                {notification.title}
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 0.5 }}>
                {notification.message}
              </Typography>
              <Typography variant="caption" color="text.secondary">
                {notification.time}
              </Typography>
            </Box>
          </MenuItem>
        ))}
        <Divider />
        <MenuItem onClick={handleNotificationMenuClose}>
          <Typography variant="body2" color="primary" sx={{ textAlign: 'center', width: '100%' }}>
            View All Notifications
          </Typography>
        </MenuItem>
      </Menu>
    </Box>
  );
};

export default ProfessionalLayout;
