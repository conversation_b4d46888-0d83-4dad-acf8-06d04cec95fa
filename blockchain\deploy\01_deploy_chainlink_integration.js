const { network } = require("hardhat");
const { developmentChains, networkConfig } = require("../helper-hardhat-config");
const { verify } = require("../utils/verify");

module.exports = async ({ getNamedAccounts, deployments }) => {
  const { deploy, log } = deployments;
  const { deployer } = await getNamedAccounts();
  const chainId = network.config.chainId;

  log("----------------------------------------------------");
  log(`Deploying KontourChainlinkIntegration on ${network.name} (chainId: ${chainId})`);
  log("----------------------------------------------------");

  // Get network-specific addresses
  let ethUsdFeed, btcUsdFeed, linkUsdFeed, vrfCoordinator, functions<PERSON><PERSON><PERSON>, ccip<PERSON>outer, linkToken;
  let vrfSubscriptionId, functionsSubscriptionId, vrfKeyHash;

  if (chainId === 1) {
    // Mainnet addresses
    ethUsdFeed = "******************************************";
    btcUsdFeed = "******************************************";
    linkUsdFeed = "******************************************";
    vrfCoordinator = "******************************************";
    functionsRouter = "******************************************";
    ccipRouter = "******************************************";
    linkToken = "******************************************";
    vrfKeyHash = "0xAA77729D3466CA35AE8D28B3BBAC7CC36A5031EFDC430821C02BC31A238AF445";
    vrfSubscriptionId = process.env.VRF_SUBSCRIPTION_ID || 1234;
    functionsSubscriptionId = process.env.FUNCTIONS_SUBSCRIPTION_ID || 5678;
  } else if (chainId === 11155111) {
    // Sepolia testnet addresses
    ethUsdFeed = "******************************************";
    btcUsdFeed = "******************************************";
    linkUsdFeed = "******************************************";
    vrfCoordinator = "******************************************";
    functionsRouter = "******************************************";
    ccipRouter = "******************************************";
    linkToken = "******************************************";
    vrfKeyHash = "0x474e34a077df58807dbe9c96d3c009b23b3c6d0cce433e59bbf5b34f823bc56c";
    vrfSubscriptionId = process.env.VRF_SUBSCRIPTION_ID || 1234;
    functionsSubscriptionId = process.env.FUNCTIONS_SUBSCRIPTION_ID || 5678;
  } else {
    // Local development - deploy mocks
    log("Local network detected! Deploying mocks...");
    
    // Deploy mock contracts for local testing
    const mockV3Aggregator = await deploy("MockV3Aggregator", {
      contract: "MockV3Aggregator",
      from: deployer,
      log: true,
      args: [8, 200000000000], // 8 decimals, $2000 initial price
    });

    const mockVRFCoordinator = await deploy("VRFCoordinatorV2Mock", {
      from: deployer,
      log: true,
      args: [100000000000000000, 1000000000], // base fee, gas price link
    });

    const mockLinkToken = await deploy("LinkToken", {
      from: deployer,
      log: true,
    });

    ethUsdFeed = mockV3Aggregator.address;
    btcUsdFeed = mockV3Aggregator.address;
    linkUsdFeed = mockV3Aggregator.address;
    vrfCoordinator = mockVRFCoordinator.address;
    functionsRouter = mockVRFCoordinator.address; // Use same mock for simplicity
    ccipRouter = mockVRFCoordinator.address; // Use same mock for simplicity
    linkToken = mockLinkToken.address;
    vrfKeyHash = "0x474e34a077df58807dbe9c96d3c009b23b3c6d0cce433e59bbf5b34f823bc56c";
    vrfSubscriptionId = 1;
    functionsSubscriptionId = 1;
  }

  // Deploy KontourToken first (if not already deployed)
  let kontourToken;
  try {
    const existingKontourToken = await deployments.get("KontourToken");
    kontourToken = existingKontourToken.address;
    log(`Using existing KontourToken at: ${kontourToken}`);
  } catch (error) {
    log("KontourToken not found, deploying...");
    const kontourTokenDeployment = await deploy("KontourToken", {
      from: deployer,
      log: true,
      args: ["Kontour Coin", "KNTOUR", 1000000000], // 1B initial supply
    });
    kontourToken = kontourTokenDeployment.address;
  }

  // Constructor arguments
  const args = [
    ethUsdFeed,
    btcUsdFeed,
    linkUsdFeed,
    vrfCoordinator,
    functionsRouter,
    ccipRouter,
    kontourToken,
    linkToken,
    vrfSubscriptionId,
    functionsSubscriptionId,
    vrfKeyHash,
  ];

  log("Constructor arguments:");
  log(`  ETH/USD Feed: ${ethUsdFeed}`);
  log(`  BTC/USD Feed: ${btcUsdFeed}`);
  log(`  LINK/USD Feed: ${linkUsdFeed}`);
  log(`  VRF Coordinator: ${vrfCoordinator}`);
  log(`  Functions Router: ${functionsRouter}`);
  log(`  CCIP Router: ${ccipRouter}`);
  log(`  Kontour Token: ${kontourToken}`);
  log(`  LINK Token: ${linkToken}`);
  log(`  VRF Subscription ID: ${vrfSubscriptionId}`);
  log(`  Functions Subscription ID: ${functionsSubscriptionId}`);
  log(`  VRF Key Hash: ${vrfKeyHash}`);

  // Deploy the contract
  const kontourChainlinkIntegration = await deploy("KontourChainlinkIntegration", {
    from: deployer,
    args: args,
    log: true,
    waitConfirmations: developmentChains.includes(network.name) ? 1 : 6,
  });

  log(`KontourChainlinkIntegration deployed to: ${kontourChainlinkIntegration.address}`);

  // Verify the contract on Etherscan (if not on local network)
  if (!developmentChains.includes(network.name) && process.env.ETHERSCAN_API_KEY) {
    log("Verifying contract on Etherscan...");
    await verify(kontourChainlinkIntegration.address, args);
  }

  // Post-deployment setup
  if (chainId === 31337) {
    // Local network setup
    log("Setting up local network...");
    
    const vrfCoordinatorV2Mock = await ethers.getContract("VRFCoordinatorV2Mock");
    const linkTokenContract = await ethers.getContract("LinkToken");
    const chainlinkIntegration = await ethers.getContract("KontourChainlinkIntegration");

    // Create VRF subscription
    const transactionResponse = await vrfCoordinatorV2Mock.createSubscription();
    const transactionReceipt = await transactionResponse.wait();
    const subscriptionId = transactionReceipt.events[0].args.subId;

    // Fund the subscription
    await vrfCoordinatorV2Mock.fundSubscription(subscriptionId, ethers.utils.parseEther("10"));

    // Add consumer
    await vrfCoordinatorV2Mock.addConsumer(subscriptionId, chainlinkIntegration.address);

    // Fund contract with LINK
    await linkTokenContract.transfer(chainlinkIntegration.address, ethers.utils.parseEther("10"));

    log(`VRF Subscription ID: ${subscriptionId}`);
    log("Local setup completed!");
  }

  // Save deployment info
  const deploymentInfo = {
    network: network.name,
    chainId: chainId,
    contractAddress: kontourChainlinkIntegration.address,
    kontourToken: kontourToken,
    chainlinkServices: {
      ethUsdFeed,
      btcUsdFeed,
      linkUsdFeed,
      vrfCoordinator,
      functionsRouter,
      ccipRouter,
      linkToken,
    },
    subscriptions: {
      vrf: vrfSubscriptionId,
      functions: functionsSubscriptionId,
    },
    deployedAt: new Date().toISOString(),
    deployer: deployer,
  };

  // Write deployment info to file
  const fs = require("fs");
  const path = require("path");
  const deploymentDir = path.join(__dirname, "../deployments");
  if (!fs.existsSync(deploymentDir)) {
    fs.mkdirSync(deploymentDir, { recursive: true });
  }
  
  fs.writeFileSync(
    path.join(deploymentDir, `${network.name}_deployment.json`),
    JSON.stringify(deploymentInfo, null, 2)
  );

  log("----------------------------------------------------");
  log("Deployment Summary:");
  log(`  Network: ${network.name}`);
  log(`  Contract: ${kontourChainlinkIntegration.address}`);
  log(`  Kontour Token: ${kontourToken}`);
  log(`  Gas Used: ${kontourChainlinkIntegration.receipt?.gasUsed || "N/A"}`);
  log(`  Deployment Info saved to: deployments/${network.name}_deployment.json`);
  log("----------------------------------------------------");

  // Instructions for next steps
  if (chainId === 1) {
    log("🚀 MAINNET DEPLOYMENT COMPLETE!");
    log("");
    log("Next steps:");
    log("1. Fund VRF subscription with LINK tokens");
    log("2. Add contract as VRF consumer");
    log("3. Fund Functions subscription");
    log("4. Add contract as Functions consumer");
    log("5. Fund contract with LINK for operations");
    log("6. Configure automation upkeep");
    log("7. Test all Chainlink services");
    log("");
    log("⚠️  IMPORTANT: Verify all subscriptions are properly funded!");
  } else if (chainId === 11155111) {
    log("🧪 SEPOLIA TESTNET DEPLOYMENT COMPLETE!");
    log("");
    log("Next steps:");
    log("1. Get testnet LINK from faucet");
    log("2. Fund VRF subscription");
    log("3. Add contract as consumer");
    log("4. Test all functionalities");
  }

  return kontourChainlinkIntegration;
};

module.exports.tags = ["all", "chainlink", "main"];
