import React from 'react';
import ChainlinkDashboard from '../components/ChainlinkDashboard';

const ChainlinkPage: React.FC = () => {
  return (
    <div className="chainlink-page">
      <div className="page-header">
        <h1>🔗 Kontour Chainlink Mainnet Integration</h1>
        <p>Complete Chainlink integration with AI-powered RAG pipeline and automated trading</p>
        <div className="feature-badges">
          <span className="badge">📊 Data Feeds</span>
          <span className="badge">🎲 VRF v2.5</span>
          <span className="badge">🔗 Functions</span>
          <span className="badge">⚙️ Automation</span>
          <span className="badge">🌐 CCIP</span>
          <span className="badge">🤖 AI RAG</span>
        </div>
      </div>
      
      <ChainlinkDashboard />
      
      <style>{`
        .chainlink-page {
          min-height: 100vh;
          background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
        }
        
        .page-header {
          padding: 40px 20px 20px 20px;
          text-align: center;
          color: white;
        }
        
        .page-header h1 {
          font-size: 2.5rem;
          margin-bottom: 15px;
          text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }
        
        .page-header p {
          font-size: 1.2rem;
          opacity: 0.9;
          max-width: 800px;
          margin: 0 auto 20px auto;
        }
        
        .feature-badges {
          display: flex;
          flex-wrap: wrap;
          justify-content: center;
          gap: 10px;
          margin-top: 20px;
        }
        
        .badge {
          padding: 8px 16px;
          background: rgba(255, 255, 255, 0.1);
          border-radius: 20px;
          font-size: 0.9rem;
          font-weight: bold;
          backdrop-filter: blur(10px);
          border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        @media (max-width: 768px) {
          .page-header h1 {
            font-size: 2rem;
          }
          
          .page-header p {
            font-size: 1rem;
          }
          
          .feature-badges {
            justify-content: center;
          }
        }
      `}</style>
    </div>
  );
};

export default ChainlinkPage;
