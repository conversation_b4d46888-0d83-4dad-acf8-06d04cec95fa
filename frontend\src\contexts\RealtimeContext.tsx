import React, { createContext, useState, useEffect, useContext, ReactNode } from 'react';
import { IntegrationContext } from './IntegrationContext';

// WebSocket URL
const WS_URL = process.env.REACT_APP_WS_URL || 'ws://localhost:8030/ws';

// Types
interface Message {
  channel: string;
  event: string;
  data: any;
  timestamp: string;
}

interface RealtimeContextType {
  isConnected: boolean;
  isConnecting: boolean;
  error: string | null;
  subscribe: (channel: string) => Promise<boolean>;
  unsubscribe: (channel: string) => Promise<boolean>;
  messages: Record<string, Message[]>;
  clearMessages: (channel?: string) => void;
}

const defaultContext: RealtimeContextType = {
  isConnected: false,
  isConnecting: false,
  error: null,
  subscribe: async () => false,
  unsubscribe: async () => false,
  messages: {},
  clearMessages: () => {}
};

export const RealtimeContext = createContext<RealtimeContextType>(defaultContext);

interface RealtimeProviderProps {
  children: ReactNode;
}

export const RealtimeProvider: React.FC<RealtimeProviderProps> = ({ children }) => {
  const { isAuthenticated, user } = useContext(IntegrationContext);
  
  const [socket, setSocket] = useState<WebSocket | null>(null);
  const [isConnected, setIsConnected] = useState<boolean>(false);
  const [isConnecting, setIsConnecting] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [messages, setMessages] = useState<Record<string, Message[]>>({});
  const [subscriptions, setSubscriptions] = useState<Set<string>>(new Set());
  
  // Connect to WebSocket when authenticated
  useEffect(() => {
    if (!isAuthenticated) {
      // Disconnect if not authenticated
      if (socket) {
        socket.close();
        setSocket(null);
        setIsConnected(false);
      }
      return;
    }
    
    // Get token from localStorage
    const token = localStorage.getItem('kontour_token');
    if (!token) {
      setError('No authentication token found');
      return;
    }
    
    // Connect to WebSocket
    const connectWebSocket = () => {
      try {
        setIsConnecting(true);
        setError(null);
        
        const ws = new WebSocket(WS_URL);
        
        ws.onopen = () => {
          // Send authentication message
          ws.send(JSON.stringify({ token }));
        };
        
        ws.onmessage = (event) => {
          const message = JSON.parse(event.data);
          
          // Handle connection confirmation
          if (message.event === 'connected') {
            setIsConnected(true);
            setIsConnecting(false);
            
            // Resubscribe to channels
            subscriptions.forEach((channel) => {
              ws.send(JSON.stringify({ action: 'subscribe', channel }));
            });
          }
          
          // Handle subscription confirmation
          else if (message.event === 'subscribed') {
            if (message.success) {
              setSubscriptions((prev) => new Set([...prev, message.channel]));
            }
          }
          
          // Handle unsubscription confirmation
          else if (message.event === 'unsubscribed') {
            if (message.success) {
              setSubscriptions((prev) => {
                const newSubscriptions = new Set(prev);
                newSubscriptions.delete(message.channel);
                return newSubscriptions;
              });
            }
          }
          
          // Handle regular messages
          else if (message.channel && message.event) {
            setMessages((prev) => {
              const channelMessages = prev[message.channel] || [];
              return {
                ...prev,
                [message.channel]: [...channelMessages, message]
              };
            });
          }
        };
        
        ws.onerror = (event) => {
          console.error('WebSocket error:', event);
          setError('WebSocket connection error');
          setIsConnecting(false);
          setIsConnected(false);
        };
        
        ws.onclose = () => {
          setIsConnected(false);
          setIsConnecting(false);
          
          // Try to reconnect after a delay
          setTimeout(() => {
            if (isAuthenticated) {
              connectWebSocket();
            }
          }, 5000);
        };
        
        setSocket(ws);
      } catch (err) {
        console.error('Error connecting to WebSocket:', err);
        setError('Failed to connect to WebSocket');
        setIsConnecting(false);
        setIsConnected(false);
      }
    };
    
    if (!socket) {
      connectWebSocket();
    }
    
    // Cleanup on unmount
    return () => {
      if (socket) {
        socket.close();
      }
    };
  }, [isAuthenticated, socket]);
  
  // Subscribe to a channel
  const subscribe = async (channel: string): Promise<boolean> => {
    if (!socket || !isConnected) {
      setError('WebSocket not connected');
      return false;
    }
    
    try {
      socket.send(JSON.stringify({ action: 'subscribe', channel }));
      return true;
    } catch (err) {
      console.error('Error subscribing to channel:', err);
      setError(`Failed to subscribe to channel ${channel}`);
      return false;
    }
  };
  
  // Unsubscribe from a channel
  const unsubscribe = async (channel: string): Promise<boolean> => {
    if (!socket || !isConnected) {
      setError('WebSocket not connected');
      return false;
    }
    
    try {
      socket.send(JSON.stringify({ action: 'unsubscribe', channel }));
      return true;
    } catch (err) {
      console.error('Error unsubscribing from channel:', err);
      setError(`Failed to unsubscribe from channel ${channel}`);
      return false;
    }
  };
  
  // Clear messages
  const clearMessages = (channel?: string) => {
    if (channel) {
      setMessages((prev) => {
        const newMessages = { ...prev };
        delete newMessages[channel];
        return newMessages;
      });
    } else {
      setMessages({});
    }
  };
  
  const contextValue: RealtimeContextType = {
    isConnected,
    isConnecting,
    error,
    subscribe,
    unsubscribe,
    messages,
    clearMessages
  };
  
  return (
    <RealtimeContext.Provider value={contextValue}>
      {children}
    </RealtimeContext.Provider>
  );
};

// Custom hook to use the Realtime context
export const useRealtime = () => {
  const context = useContext(RealtimeContext);
  if (!context) {
    throw new Error('useRealtime must be used within a RealtimeProvider');
  }
  return context;
};

export default RealtimeProvider;