@echo off
setlocal enabledelayedexpansion

echo ===============================================================================
echo                    KONTOUR COIN COMPLETE BLOCKCHAIN WORKFLOW
echo ===============================================================================
echo.
echo 🚀 Starting complete blockchain workflow execution...
echo.

:: Set colors for output
set "GREEN=[92m"
set "YELLOW=[93m"
set "RED=[91m"
set "BLUE=[94m"
set "PURPLE=[95m"
set "CYAN=[96m"
set "WHITE=[97m"
set "RESET=[0m"

:: Check if Node.js is installed
echo %CYAN%🔍 Checking prerequisites...%RESET%
node --version >nul 2>&1
if errorlevel 1 (
    echo %RED%❌ Node.js is not installed or not in PATH%RESET%
    echo Please install Node.js from https://nodejs.org/
    pause
    exit /b 1
)

:: Check if npm is available
npm --version >nul 2>&1
if errorlevel 1 (
    echo %RED%❌ npm is not available%RESET%
    pause
    exit /b 1
)

echo %GREEN%✅ Node.js and npm are available%RESET%

:: Navigate to blockchain directory
cd /d "%~dp0.."
echo %BLUE%📁 Working directory: %CD%%RESET%

:: Install dependencies if node_modules doesn't exist
if not exist "node_modules" (
    echo %YELLOW%📦 Installing blockchain dependencies...%RESET%
    call npm install
    if errorlevel 1 (
        echo %RED%❌ Failed to install dependencies%RESET%
        pause
        exit /b 1
    )
    echo %GREEN%✅ Dependencies installed successfully%RESET%
) else (
    echo %GREEN%✅ Dependencies already installed%RESET%
)

:: Check if .env file exists
if not exist ".env" (
    echo %YELLOW%⚠️  .env file not found. Creating template...%RESET%
    (
        echo # Kontour Coin Blockchain Configuration
        echo MAINNET_RPC_URL=https://eth-mainnet.g.alchemy.com/v2/YOUR_API_KEY
        echo SEPOLIA_RPC_URL=https://eth-sepolia.g.alchemy.com/v2/YOUR_API_KEY
        echo PRIVATE_KEY=0xYOUR_PRIVATE_KEY_HERE
        echo ETHERSCAN_API_KEY=YOUR_ETHERSCAN_API_KEY
        echo VRF_SUBSCRIPTION_ID=1234
        echo FUNCTIONS_SUBSCRIPTION_ID=5678
        echo OPENAI_API_KEY=YOUR_OPENAI_API_KEY
    ) > .env
    echo %YELLOW%📝 Please configure .env file with your settings%RESET%
)

:: Display menu
:menu
echo.
echo %PURPLE%===============================================================================%RESET%
echo %PURPLE%                        BLOCKCHAIN WORKFLOW MENU%RESET%
echo %PURPLE%===============================================================================%RESET%
echo.
echo %WHITE%1.%RESET% %CYAN%🔨 Compile Contracts%RESET%
echo %WHITE%2.%RESET% %CYAN%🧪 Run Tests%RESET%
echo %WHITE%3.%RESET% %CYAN%🌐 Start Local Hardhat Node%RESET%
echo %WHITE%4.%RESET% %CYAN%🚀 Deploy to Local Network%RESET%
echo %WHITE%5.%RESET% %CYAN%🔗 Deploy to Sepolia Testnet%RESET%
echo %WHITE%6.%RESET% %CYAN%⚡ Deploy to Mainnet%RESET%
echo %WHITE%7.%RESET% %CYAN%🔄 Run Blockchain Workflow (Local)%RESET%
echo %WHITE%8.%RESET% %CYAN%🔄 Run Blockchain Workflow (Sepolia)%RESET%
echo %WHITE%9.%RESET% %CYAN%🔄 Run Blockchain Workflow (Mainnet)%RESET%
echo %WHITE%10.%RESET% %PURPLE%🎼 Run Complete Orchestration (Local)%RESET%
echo %WHITE%11.%RESET% %PURPLE%🎼 Run Complete Orchestration (Sepolia)%RESET%
echo %WHITE%12.%RESET% %PURPLE%🎼 Run Complete Orchestration (Mainnet)%RESET%
echo %WHITE%13.%RESET% %GREEN%🚀 Start Complete Ecosystem%RESET%
echo %WHITE%14.%RESET% %YELLOW%📊 View Deployment Reports%RESET%
echo %WHITE%15.%RESET% %YELLOW%🔍 Check Contract Verification%RESET%
echo %WHITE%0.%RESET% %RED%❌ Exit%RESET%
echo.
set /p choice=%WHITE%Enter your choice (0-15): %RESET%

if "%choice%"=="1" goto compile
if "%choice%"=="2" goto test
if "%choice%"=="3" goto start_node
if "%choice%"=="4" goto deploy_local
if "%choice%"=="5" goto deploy_sepolia
if "%choice%"=="6" goto deploy_mainnet
if "%choice%"=="7" goto workflow_local
if "%choice%"=="8" goto workflow_sepolia
if "%choice%"=="9" goto workflow_mainnet
if "%choice%"=="10" goto orchestrate_local
if "%choice%"=="11" goto orchestrate_sepolia
if "%choice%"=="12" goto orchestrate_mainnet
if "%choice%"=="13" goto start_ecosystem
if "%choice%"=="14" goto view_reports
if "%choice%"=="15" goto check_verification
if "%choice%"=="0" goto exit

echo %RED%❌ Invalid choice. Please try again.%RESET%
goto menu

:compile
echo.
echo %CYAN%🔨 Compiling smart contracts...%RESET%
call npx hardhat compile
if errorlevel 1 (
    echo %RED%❌ Compilation failed%RESET%
    pause
    goto menu
)
echo %GREEN%✅ Contracts compiled successfully%RESET%
pause
goto menu

:test
echo.
echo %CYAN%🧪 Running contract tests...%RESET%
call npm test
if errorlevel 1 (
    echo %RED%❌ Tests failed%RESET%
    pause
    goto menu
)
echo %GREEN%✅ All tests passed%RESET%
pause
goto menu

:start_node
echo.
echo %CYAN%🌐 Starting local Hardhat node...%RESET%
echo %YELLOW%Press Ctrl+C to stop the node%RESET%
call npx hardhat node
pause
goto menu

:deploy_local
echo.
echo %CYAN%🚀 Deploying contracts to local network...%RESET%
call npm run deploy:local
if errorlevel 1 (
    echo %RED%❌ Local deployment failed%RESET%
    pause
    goto menu
)
echo %GREEN%✅ Local deployment completed%RESET%
pause
goto menu

:deploy_sepolia
echo.
echo %YELLOW%⚠️  Deploying to Sepolia testnet...%RESET%
echo %YELLOW%Make sure you have Sepolia ETH and your .env is configured%RESET%
set /p confirm=Continue? (y/N): 
if /i not "%confirm%"=="y" goto menu

call npm run deploy:sepolia
if errorlevel 1 (
    echo %RED%❌ Sepolia deployment failed%RESET%
    pause
    goto menu
)
echo %GREEN%✅ Sepolia deployment completed%RESET%
pause
goto menu

:deploy_mainnet
echo.
echo %RED%🚨 MAINNET DEPLOYMENT WARNING 🚨%RESET%
echo %RED%This will deploy contracts to Ethereum mainnet using real ETH%RESET%
echo %RED%Make sure you have sufficient ETH and your .env is properly configured%RESET%
echo.
set /p confirm1=Are you sure you want to deploy to mainnet? (y/N): 
if /i not "%confirm1%"=="y" goto menu

set /p confirm2=Type 'MAINNET' to confirm: 
if not "%confirm2%"=="MAINNET" (
    echo %RED%❌ Confirmation failed%RESET%
    goto menu
)

call npm run deploy:mainnet
if errorlevel 1 (
    echo %RED%❌ Mainnet deployment failed%RESET%
    pause
    goto menu
)
echo %GREEN%✅ Mainnet deployment completed%RESET%
pause
goto menu

:workflow_local
echo.
echo %CYAN%🔄 Running blockchain workflow on local network...%RESET%
call npm run workflow:run:local
if errorlevel 1 (
    echo %RED%❌ Local workflow failed%RESET%
    pause
    goto menu
)
echo %GREEN%✅ Local workflow completed%RESET%
pause
goto menu

:workflow_sepolia
echo.
echo %CYAN%🔄 Running blockchain workflow on Sepolia...%RESET%
call npm run workflow:run:sepolia
if errorlevel 1 (
    echo %RED%❌ Sepolia workflow failed%RESET%
    pause
    goto menu
)
echo %GREEN%✅ Sepolia workflow completed%RESET%
pause
goto menu

:workflow_mainnet
echo.
echo %RED%⚠️  Running blockchain workflow on MAINNET...%RESET%
set /p confirm=Continue with mainnet workflow? (y/N): 
if /i not "%confirm%"=="y" goto menu

call npm run workflow:run:mainnet
if errorlevel 1 (
    echo %RED%❌ Mainnet workflow failed%RESET%
    pause
    goto menu
)
echo %GREEN%✅ Mainnet workflow completed%RESET%
pause
goto menu

:orchestrate_local
echo.
echo %PURPLE%🎼 Running complete orchestration on local network...%RESET%
call npm run orchestrate:local
if errorlevel 1 (
    echo %RED%❌ Local orchestration failed%RESET%
    pause
    goto menu
)
echo %GREEN%✅ Local orchestration completed%RESET%
pause
goto menu

:orchestrate_sepolia
echo.
echo %PURPLE%🎼 Running complete orchestration on Sepolia...%RESET%
call npm run orchestrate:sepolia
if errorlevel 1 (
    echo %RED%❌ Sepolia orchestration failed%RESET%
    pause
    goto menu
)
echo %GREEN%✅ Sepolia orchestration completed%RESET%
pause
goto menu

:orchestrate_mainnet
echo.
echo %RED%🚨 MAINNET ORCHESTRATION WARNING 🚨%RESET%
echo %RED%This will execute complete workflow on mainnet%RESET%
set /p confirm=Continue with mainnet orchestration? (y/N): 
if /i not "%confirm%"=="y" goto menu

call npm run orchestrate:mainnet
if errorlevel 1 (
    echo %RED%❌ Mainnet orchestration failed%RESET%
    pause
    goto menu
)
echo %GREEN%✅ Mainnet orchestration completed%RESET%
pause
goto menu

:start_ecosystem
echo.
echo %GREEN%🚀 Starting complete Kontour ecosystem...%RESET%
echo %YELLOW%This will start all services including blockchain%RESET%
cd /d "%~dp0..\.."
call start-kontour-complete-ecosystem.bat
pause
goto menu

:view_reports
echo.
echo %YELLOW%📊 Viewing deployment reports...%RESET%
if exist "reports" (
    dir reports /b
    echo.
    set /p report_file=Enter report filename to view (or press Enter to skip): 
    if not "!report_file!"=="" (
        if exist "reports\!report_file!" (
            type "reports\!report_file!"
        ) else (
            echo %RED%❌ Report file not found%RESET%
        )
    )
) else (
    echo %YELLOW%No reports directory found%RESET%
)
pause
goto menu

:check_verification
echo.
echo %YELLOW%🔍 Checking contract verification status...%RESET%
if exist "deployments" (
    echo Available deployments:
    dir deployments /b
    echo.
    set /p network=Enter network name to check: 
    if exist "deployments\!network!" (
        dir "deployments\!network!" /b
    ) else (
        echo %RED%❌ Network deployment not found%RESET%
    )
) else (
    echo %YELLOW%No deployments found%RESET%
)
pause
goto menu

:exit
echo.
echo %GREEN%👋 Thank you for using Kontour Coin Blockchain Workflow!%RESET%
echo %CYAN%🌟 Visit our platform at: http://localhost:3000%RESET%
echo.
pause
exit /b 0
