import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Button,
  Chip,
  Avatar,
  LinearProgress,
  IconButton,
  Menu,
  MenuItem,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  ListItemSecondaryAction,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  useTheme,
  Skeleton
} from '@mui/material';
import {
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  AccountBalanceWallet as WalletIcon,
  Psychology as AIIcon,
  Science as QuantumIcon,
  Security as SecurityIcon,
  MoreVert as MoreIcon,
  Launch as LaunchIcon,
  Refresh as RefreshIcon,
  Timeline as TimelineIcon,
  Assessment as AssessmentIcon,
  Speed as SpeedIcon,
  Shield as ShieldIcon,
  AutoAwesome as AutoAwesomeIcon,
  Bolt as BoltIcon
} from '@mui/icons-material';
import { styled, alpha } from '@mui/material/styles';
import { <PERSON><PERSON><PERSON>, Line, AreaChart, Area, XAxis, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ianG<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>ons<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON> } from 'recharts';

// Styled Components
const MetricCard = styled(Card)(({ theme }) => ({
  background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.05)} 0%, ${alpha(theme.palette.secondary.main, 0.05)} 100%)`,
  border: `1px solid ${alpha(theme.palette.primary.main, 0.12)}`,
  borderRadius: 16,
  overflow: 'hidden',
  position: 'relative',
  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: 3,
    background: `linear-gradient(90deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`,
  },
}));

const GlassCard = styled(Card)(({ theme }) => ({
  background: alpha(theme.palette.background.paper, 0.8),
  backdropFilter: 'blur(20px)',
  border: `1px solid ${alpha(theme.palette.divider, 0.12)}`,
  borderRadius: 20,
}));

const StatusIndicator = styled(Box)<{ status: 'success' | 'warning' | 'error' }>(({ theme, status }) => ({
  width: 8,
  height: 8,
  borderRadius: '50%',
  backgroundColor: 
    status === 'success' ? theme.palette.success.main :
    status === 'warning' ? theme.palette.warning.main :
    theme.palette.error.main,
  animation: 'pulse 2s infinite',
  '@keyframes pulse': {
    '0%': { opacity: 1 },
    '50%': { opacity: 0.5 },
    '100%': { opacity: 1 },
  },
}));

const ProfessionalDashboard: React.FC = () => {
  const theme = useTheme();
  const [loading, setLoading] = useState(true);
  const [menuAnchor, setMenuAnchor] = useState<null | HTMLElement>(null);

  // Mock data
  const portfolioData = [
    { name: 'Jan', value: 45000 },
    { name: 'Feb', value: 52000 },
    { name: 'Mar', value: 48000 },
    { name: 'Apr', value: 61000 },
    { name: 'May', value: 55000 },
    { name: 'Jun', value: 67000 },
    { name: 'Jul', value: 73000 },
  ];

  const assetAllocation = [
    { name: 'Bitcoin', value: 45, color: '#f7931a' },
    { name: 'Ethereum', value: 30, color: '#627eea' },
    { name: 'Kontour', value: 15, color: '#9c27b0' },
    { name: 'Others', value: 10, color: '#757575' },
  ];

  const recentTransactions = [
    { id: 1, type: 'buy', asset: 'BTC', amount: '0.5', value: '$32,500', time: '2 min ago', status: 'completed' },
    { id: 2, type: 'sell', asset: 'ETH', amount: '2.3', value: '$4,600', time: '15 min ago', status: 'completed' },
    { id: 3, type: 'buy', asset: 'KTC', amount: '1000', value: '$2,500', time: '1 hour ago', status: 'pending' },
  ];

  const aiInsights = [
    { id: 1, title: 'Market Bullish Signal', confidence: 87, type: 'positive', time: '5 min ago' },
    { id: 2, title: 'Risk Alert: High Volatility', confidence: 92, type: 'warning', time: '12 min ago' },
    { id: 3, title: 'Portfolio Rebalance Suggested', confidence: 78, type: 'info', time: '1 hour ago' },
  ];

  useEffect(() => {
    // Simulate loading
    const timer = setTimeout(() => setLoading(false), 1500);
    return () => clearTimeout(timer);
  }, []);

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setMenuAnchor(event.currentTarget);
  };

  const handleMenuClose = () => {
    setMenuAnchor(null);
  };

  if (loading) {
    return (
      <Box sx={{ p: 3 }}>
        <Grid container spacing={3}>
          {[...Array(8)].map((_, index) => (
            <Grid item xs={12} sm={6} md={3} key={index}>
              <Card>
                <CardContent>
                  <Skeleton variant="text" width="60%" />
                  <Skeleton variant="text" width="40%" />
                  <Skeleton variant="rectangular" height={60} sx={{ mt: 1 }} />
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ mb: 4, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Box>
          <Typography variant="h4" fontWeight={700} gutterBottom>
            Welcome back! 👋
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Here's what's happening with your portfolio today
          </Typography>
        </Box>
        <Box sx={{ display: 'flex', gap: 1 }}>
          <Button variant="outlined" startIcon={<RefreshIcon />}>
            Refresh
          </Button>
          <Button variant="contained" startIcon={<LaunchIcon />}>
            Quick Trade
          </Button>
        </Box>
      </Box>

      {/* Key Metrics */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <MetricCard>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
                <Avatar sx={{ bgcolor: theme.palette.success.main, width: 48, height: 48 }}>
                  <WalletIcon />
                </Avatar>
                <Chip label="+12.5%" color="success" size="small" />
              </Box>
              <Typography variant="h4" fontWeight={700} gutterBottom>
                $73,245
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Total Portfolio Value
              </Typography>
            </CardContent>
          </MetricCard>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <MetricCard>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
                <Avatar sx={{ bgcolor: theme.palette.info.main, width: 48, height: 48 }}>
                  <TrendingUpIcon />
                </Avatar>
                <Chip label="+8.2%" color="success" size="small" />
              </Box>
              <Typography variant="h4" fontWeight={700} gutterBottom>
                $8,245
              </Typography>
              <Typography variant="body2" color="text.secondary">
                24h P&L
              </Typography>
            </CardContent>
          </MetricCard>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <MetricCard>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
                <Avatar sx={{ bgcolor: theme.palette.primary.main, width: 48, height: 48 }}>
                  <AIIcon />
                </Avatar>
                <StatusIndicator status="success" />
              </Box>
              <Typography variant="h4" fontWeight={700} gutterBottom>
                8/8
              </Typography>
              <Typography variant="body2" color="text.secondary">
                AI Agents Active
              </Typography>
            </CardContent>
          </MetricCard>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <MetricCard>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
                <Avatar sx={{ bgcolor: theme.palette.secondary.main, width: 48, height: 48 }}>
                  <QuantumIcon />
                </Avatar>
                <StatusIndicator status="success" />
              </Box>
              <Typography variant="h4" fontWeight={700} gutterBottom>
                12
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Active Workflows
              </Typography>
            </CardContent>
          </MetricCard>
        </Grid>
      </Grid>

      {/* Charts and Analytics */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} md={8}>
          <GlassCard>
            <CardContent>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
                <Typography variant="h6" fontWeight={600}>
                  Portfolio Performance
                </Typography>
                <IconButton onClick={handleMenuOpen}>
                  <MoreIcon />
                </IconButton>
              </Box>
              <ResponsiveContainer width="100%" height={300}>
                <AreaChart data={portfolioData}>
                  <defs>
                    <linearGradient id="colorValue" x1="0" y1="0" x2="0" y2="1">
                      <stop offset="5%" stopColor={theme.palette.primary.main} stopOpacity={0.3}/>
                      <stop offset="95%" stopColor={theme.palette.primary.main} stopOpacity={0}/>
                    </linearGradient>
                  </defs>
                  <CartesianGrid strokeDasharray="3 3" stroke={alpha(theme.palette.divider, 0.3)} />
                  <XAxis dataKey="name" stroke={theme.palette.text.secondary} />
                  <YAxis stroke={theme.palette.text.secondary} />
                  <Tooltip 
                    contentStyle={{
                      backgroundColor: theme.palette.background.paper,
                      border: `1px solid ${alpha(theme.palette.divider, 0.12)}`,
                      borderRadius: 8,
                    }}
                  />
                  <Area 
                    type="monotone" 
                    dataKey="value" 
                    stroke={theme.palette.primary.main}
                    strokeWidth={3}
                    fillOpacity={1} 
                    fill="url(#colorValue)" 
                  />
                </AreaChart>
              </ResponsiveContainer>
            </CardContent>
          </GlassCard>
        </Grid>

        <Grid item xs={12} md={4}>
          <GlassCard>
            <CardContent>
              <Typography variant="h6" fontWeight={600} gutterBottom>
                Asset Allocation
              </Typography>
              <ResponsiveContainer width="100%" height={200}>
                <PieChart>
                  <Pie
                    data={assetAllocation}
                    cx="50%"
                    cy="50%"
                    innerRadius={40}
                    outerRadius={80}
                    paddingAngle={5}
                    dataKey="value"
                  >
                    {assetAllocation.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip />
                </PieChart>
              </ResponsiveContainer>
              <Box sx={{ mt: 2 }}>
                {assetAllocation.map((asset) => (
                  <Box key={asset.name} sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                    <Box sx={{ width: 12, height: 12, bgcolor: asset.color, borderRadius: '50%', mr: 1 }} />
                    <Typography variant="body2" sx={{ flex: 1 }}>
                      {asset.name}
                    </Typography>
                    <Typography variant="body2" fontWeight={600}>
                      {asset.value}%
                    </Typography>
                  </Box>
                ))}
              </Box>
            </CardContent>
          </GlassCard>
        </Grid>
      </Grid>

      {/* Recent Activity and AI Insights */}
      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <GlassCard>
            <CardContent>
              <Typography variant="h6" fontWeight={600} gutterBottom>
                Recent Transactions
              </Typography>
              <List disablePadding>
                {recentTransactions.map((transaction) => (
                  <ListItem key={transaction.id} disablePadding>
                    <ListItemIcon>
                      <Avatar sx={{ 
                        width: 32, 
                        height: 32, 
                        bgcolor: transaction.type === 'buy' ? theme.palette.success.main : theme.palette.error.main 
                      }}>
                        {transaction.type === 'buy' ? <TrendingUpIcon /> : <TrendingDownIcon />}
                      </Avatar>
                    </ListItemIcon>
                    <ListItemText
                      primary={`${transaction.type.toUpperCase()} ${transaction.asset}`}
                      secondary={`${transaction.amount} • ${transaction.time}`}
                    />
                    <ListItemSecondaryAction>
                      <Box sx={{ textAlign: 'right' }}>
                        <Typography variant="body2" fontWeight={600}>
                          {transaction.value}
                        </Typography>
                        <Chip 
                          label={transaction.status} 
                          size="small" 
                          color={transaction.status === 'completed' ? 'success' : 'warning'}
                        />
                      </Box>
                    </ListItemSecondaryAction>
                  </ListItem>
                ))}
              </List>
            </CardContent>
          </GlassCard>
        </Grid>

        <Grid item xs={12} md={6}>
          <GlassCard>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <AutoAwesomeIcon sx={{ mr: 1, color: theme.palette.primary.main }} />
                <Typography variant="h6" fontWeight={600}>
                  AI Insights
                </Typography>
              </Box>
              <List disablePadding>
                {aiInsights.map((insight) => (
                  <ListItem key={insight.id} disablePadding sx={{ mb: 2 }}>
                    <ListItemIcon>
                      <Avatar sx={{ 
                        width: 32, 
                        height: 32, 
                        bgcolor: insight.type === 'positive' ? theme.palette.success.main : 
                                insight.type === 'warning' ? theme.palette.warning.main : 
                                theme.palette.info.main
                      }}>
                        <BoltIcon />
                      </Avatar>
                    </ListItemIcon>
                    <ListItemText
                      primary={insight.title}
                      secondary={
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mt: 0.5 }}>
                          <Typography variant="caption">
                            Confidence: {insight.confidence}%
                          </Typography>
                          <LinearProgress 
                            variant="determinate" 
                            value={insight.confidence} 
                            sx={{ flex: 1, height: 4, borderRadius: 2 }}
                          />
                          <Typography variant="caption" color="text.secondary">
                            {insight.time}
                          </Typography>
                        </Box>
                      }
                    />
                  </ListItem>
                ))}
              </List>
            </CardContent>
          </GlassCard>
        </Grid>
      </Grid>

      {/* Menu */}
      <Menu
        anchorEl={menuAnchor}
        open={Boolean(menuAnchor)}
        onClose={handleMenuClose}
      >
        <MenuItem onClick={handleMenuClose}>
          <ListItemIcon><TimelineIcon /></ListItemIcon>
          View Details
        </MenuItem>
        <MenuItem onClick={handleMenuClose}>
          <ListItemIcon><AssessmentIcon /></ListItemIcon>
          Export Data
        </MenuItem>
        <MenuItem onClick={handleMenuClose}>
          <ListItemIcon><RefreshIcon /></ListItemIcon>
          Refresh
        </MenuItem>
      </Menu>
    </Box>
  );
};

export default ProfessionalDashboard;
