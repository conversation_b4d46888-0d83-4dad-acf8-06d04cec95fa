import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  Ty<PERSON>graphy,
  Grid,
  Chip,
  LinearProgress,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Switch,
  FormControlLabel,
  Alert,
  Tabs,
  Tab,
  IconButton,
  Tooltip
} from '@mui/material';
import {
  SmartToy as BotIcon,
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  PlayArrow as EnableIcon,
  Pause as DisableIcon,
  Refresh as RefreshIcon,
  Assessment as MetricsIcon,
  History as HistoryIcon,
  Settings as SettingsIcon
} from '@mui/icons-material';
import { styled } from '@mui/material/styles';

// Styled components
const MetricCard = styled(Card)(({ theme }) => ({
  background: `linear-gradient(135deg, ${theme.palette.primary.main}20, ${theme.palette.secondary.main}20)`,
  border: `1px solid ${theme.palette.primary.main}30`
}));

const StatusChip = styled(Chip)<{ status: 'active' | 'inactive' }>(({ theme, status }) => ({
  backgroundColor: status === 'active' ? theme.palette.success.main : theme.palette.error.main,
  color: theme.palette.common.white
}));

// Types
interface Agent {
  agent_id: string;
  name: string;
  role: string;
  llm_provider: string;
  model_name: string;
  temperature: number;
  max_tokens: number;
  enabled: boolean;
  performance: {
    total_requests: number;
    successful_responses: number;
    success_rate: number;
    average_response_time: number;
  };
}

interface PerformanceMetrics {
  total_agents: number;
  active_agents: number;
  total_requests: number;
  total_successful_responses: number;
  overall_success_rate: number;
  average_response_time: number;
  consensus_count: number;
  platform_connected: boolean;
}

interface ConsensusHistory {
  consensus_id: string;
  query: string;
  consensus_response: string;
  confidence_score: number;
  agreement_level: number;
  participating_agents: string[];
  timestamp: string;
}

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const TabPanel: React.FC<TabPanelProps> = ({ children, value, index }) => (
  <div hidden={value !== index}>
    {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
  </div>
);

const AIAgentDashboard: React.FC = () => {
  const [agents, setAgents] = useState<Agent[]>([]);
  const [metrics, setMetrics] = useState<PerformanceMetrics | null>(null);
  const [consensusHistory, setConsensusHistory] = useState<ConsensusHistory[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [tabValue, setTabValue] = useState(0);
  const [addAgentOpen, setAddAgentOpen] = useState(false);
  const [newAgent, setNewAgent] = useState({
    agent_id: '',
    name: '',
    role: '',
    llm_provider: '',
    model_name: '',
    temperature: 0.7,
    max_tokens: 1000,
    system_prompt: '',
    enabled: true
  });

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    setLoading(true);
    try {
      await Promise.all([
        loadAgents(),
        loadMetrics(),
        loadConsensusHistory()
      ]);
    } catch (error) {
      console.error('Failed to load data:', error);
      setError('Failed to load dashboard data');
    } finally {
      setLoading(false);
    }
  };

  const loadAgents = async () => {
    try {
      const response = await fetch('http://localhost:8025/agents');
      if (response.ok) {
        const agentsData = await response.json();
        const agentsList = Object.entries(agentsData).map(([id, data]: [string, any]) => ({
          agent_id: id,
          ...data
        }));
        setAgents(agentsList);
      }
    } catch (error) {
      console.error('Failed to load agents:', error);
    }
  };

  const loadMetrics = async () => {
    try {
      const response = await fetch('http://localhost:8025/performance');
      if (response.ok) {
        const metricsData = await response.json();
        setMetrics(metricsData);
      }
    } catch (error) {
      console.error('Failed to load metrics:', error);
    }
  };

  const loadConsensusHistory = async () => {
    try {
      const response = await fetch('http://localhost:8025/consensus/history?limit=20');
      if (response.ok) {
        const historyData = await response.json();
        setConsensusHistory(historyData);
      }
    } catch (error) {
      console.error('Failed to load consensus history:', error);
    }
  };

  const toggleAgent = async (agentId: string, enable: boolean) => {
    try {
      const endpoint = enable ? 'enable' : 'disable';
      const response = await fetch(`http://localhost:8025/agents/${agentId}/${endpoint}`, {
        method: 'POST'
      });
      
      if (response.ok) {
        await loadAgents();
        await loadMetrics();
      }
    } catch (error) {
      console.error(`Failed to ${enable ? 'enable' : 'disable'} agent:`, error);
    }
  };

  const addAgent = async () => {
    try {
      const response = await fetch('http://localhost:8025/agents', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(newAgent)
      });

      if (response.ok) {
        setAddAgentOpen(false);
        setNewAgent({
          agent_id: '',
          name: '',
          role: '',
          llm_provider: '',
          model_name: '',
          temperature: 0.7,
          max_tokens: 1000,
          system_prompt: '',
          enabled: true
        });
        await loadAgents();
        await loadMetrics();
      } else {
        const errorData = await response.json();
        setError(errorData.detail || 'Failed to add agent');
      }
    } catch (error) {
      console.error('Failed to add agent:', error);
      setError('Failed to add agent');
    }
  };

  const runDemo = async () => {
    setLoading(true);
    try {
      const response = await fetch('http://localhost:8025/demo', {
        method: 'POST'
      });
      
      if (response.ok) {
        const demoData = await response.json();
        console.log('Demo completed:', demoData);
        await loadMetrics();
        await loadConsensusHistory();
      }
    } catch (error) {
      console.error('Demo failed:', error);
      setError('Demo failed');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Box sx={{ p: 3 }}>
      <Box sx={{ display: 'flex', justifyContent: 'between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" component="h1">
          🤖 AI Agent Dashboard
        </Typography>
        <Box sx={{ display: 'flex', gap: 1 }}>
          <Button
            variant="outlined"
            startIcon={<RefreshIcon />}
            onClick={loadData}
            disabled={loading}
          >
            Refresh
          </Button>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={() => setAddAgentOpen(true)}
          >
            Add Agent
          </Button>
          <Button
            variant="contained"
            color="secondary"
            onClick={runDemo}
            disabled={loading}
          >
            Run Demo
          </Button>
        </Box>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      {/* Metrics Overview */}
      {metrics && (
        <Grid container spacing={3} sx={{ mb: 3 }}>
          <Grid item xs={12} sm={6} md={3}>
            <MetricCard>
              <CardContent>
                <Typography variant="h6" color="primary">
                  {metrics.total_agents}
                </Typography>
                <Typography variant="body2" color="textSecondary">
                  Total Agents
                </Typography>
              </CardContent>
            </MetricCard>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <MetricCard>
              <CardContent>
                <Typography variant="h6" color="primary">
                  {metrics.active_agents}
                </Typography>
                <Typography variant="body2" color="textSecondary">
                  Active Agents
                </Typography>
              </CardContent>
            </MetricCard>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <MetricCard>
              <CardContent>
                <Typography variant="h6" color="primary">
                  {(metrics.overall_success_rate * 100).toFixed(1)}%
                </Typography>
                <Typography variant="body2" color="textSecondary">
                  Success Rate
                </Typography>
              </CardContent>
            </MetricCard>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <MetricCard>
              <CardContent>
                <Typography variant="h6" color="primary">
                  {metrics.consensus_count}
                </Typography>
                <Typography variant="body2" color="textSecondary">
                  Consensus Queries
                </Typography>
              </CardContent>
            </MetricCard>
          </Grid>
        </Grid>
      )}

      {/* Tabs */}
      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
        <Tabs value={tabValue} onChange={(_, newValue) => setTabValue(newValue)}>
          <Tab label="Agents" icon={<BotIcon />} />
          <Tab label="Performance" icon={<MetricsIcon />} />
          <Tab label="Consensus History" icon={<HistoryIcon />} />
        </Tabs>
      </Box>

      {/* Agents Tab */}
      <TabPanel value={tabValue} index={0}>
        <TableContainer component={Paper}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Agent</TableCell>
                <TableCell>Role</TableCell>
                <TableCell>LLM Provider</TableCell>
                <TableCell>Status</TableCell>
                <TableCell>Success Rate</TableCell>
                <TableCell>Avg Response Time</TableCell>
                <TableCell>Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {agents.map((agent) => (
                <TableRow key={agent.agent_id}>
                  <TableCell>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <BotIcon />
                      <Box>
                        <Typography variant="body2" fontWeight="bold">
                          {agent.name}
                        </Typography>
                        <Typography variant="caption" color="textSecondary">
                          {agent.agent_id}
                        </Typography>
                      </Box>
                    </Box>
                  </TableCell>
                  <TableCell>
                    <Chip
                      label={agent.role.replace('_', ' ')}
                      size="small"
                      variant="outlined"
                    />
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2">
                      {agent.llm_provider}
                    </Typography>
                    <Typography variant="caption" color="textSecondary">
                      {agent.model_name}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <StatusChip
                      label={agent.enabled ? 'Active' : 'Inactive'}
                      status={agent.enabled ? 'active' : 'inactive'}
                      size="small"
                    />
                  </TableCell>
                  <TableCell>
                    <Box>
                      <Typography variant="body2">
                        {(agent.performance.success_rate * 100).toFixed(1)}%
                      </Typography>
                      <LinearProgress
                        variant="determinate"
                        value={agent.performance.success_rate * 100}
                        sx={{ mt: 0.5, height: 4 }}
                      />
                    </Box>
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2">
                      {agent.performance.average_response_time.toFixed(2)}s
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Box sx={{ display: 'flex', gap: 0.5 }}>
                      <Tooltip title={agent.enabled ? 'Disable' : 'Enable'}>
                        <IconButton
                          size="small"
                          onClick={() => toggleAgent(agent.agent_id, !agent.enabled)}
                        >
                          {agent.enabled ? <DisableIcon /> : <EnableIcon />}
                        </IconButton>
                      </Tooltip>
                      <Tooltip title="Edit">
                        <IconButton size="small">
                          <EditIcon />
                        </IconButton>
                      </Tooltip>
                    </Box>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </TabPanel>

      {/* Performance Tab */}
      <TabPanel value={tabValue} index={1}>
        <Grid container spacing={3}>
          {agents.map((agent) => (
            <Grid item xs={12} md={6} key={agent.agent_id}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    {agent.name}
                  </Typography>
                  <Box sx={{ mb: 2 }}>
                    <Typography variant="body2" color="textSecondary">
                      Total Requests: {agent.performance.total_requests}
                    </Typography>
                    <Typography variant="body2" color="textSecondary">
                      Successful: {agent.performance.successful_responses}
                    </Typography>
                    <Typography variant="body2" color="textSecondary">
                      Avg Response Time: {agent.performance.average_response_time.toFixed(2)}s
                    </Typography>
                  </Box>
                  <Box>
                    <Typography variant="body2" gutterBottom>
                      Success Rate: {(agent.performance.success_rate * 100).toFixed(1)}%
                    </Typography>
                    <LinearProgress
                      variant="determinate"
                      value={agent.performance.success_rate * 100}
                      sx={{ height: 8, borderRadius: 4 }}
                    />
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
      </TabPanel>

      {/* Consensus History Tab */}
      <TabPanel value={tabValue} index={2}>
        <TableContainer component={Paper}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Query</TableCell>
                <TableCell>Confidence</TableCell>
                <TableCell>Agreement</TableCell>
                <TableCell>Agents</TableCell>
                <TableCell>Timestamp</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {consensusHistory.map((consensus) => (
                <TableRow key={consensus.consensus_id}>
                  <TableCell>
                    <Typography variant="body2" sx={{ maxWidth: 300 }}>
                      {consensus.query.length > 100 
                        ? consensus.query.substring(0, 100) + '...'
                        : consensus.query
                      }
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Chip
                      label={`${(consensus.confidence_score * 100).toFixed(0)}%`}
                      size="small"
                      color={consensus.confidence_score > 0.7 ? 'success' : 'warning'}
                    />
                  </TableCell>
                  <TableCell>
                    <Chip
                      label={`${(consensus.agreement_level * 100).toFixed(0)}%`}
                      size="small"
                      color={consensus.agreement_level > 0.7 ? 'success' : 'warning'}
                    />
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2">
                      {consensus.participating_agents.length} agents
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2">
                      {new Date(consensus.timestamp).toLocaleString()}
                    </Typography>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </TabPanel>

      {/* Add Agent Dialog */}
      <Dialog open={addAgentOpen} onClose={() => setAddAgentOpen(false)} maxWidth="md" fullWidth>
        <DialogTitle>Add New AI Agent</DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Agent ID"
                value={newAgent.agent_id}
                onChange={(e) => setNewAgent({ ...newAgent, agent_id: e.target.value })}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Agent Name"
                value={newAgent.name}
                onChange={(e) => setNewAgent({ ...newAgent, name: e.target.value })}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>Role</InputLabel>
                <Select
                  value={newAgent.role}
                  onChange={(e) => setNewAgent({ ...newAgent, role: e.target.value })}
                >
                  <MenuItem value="trading_advisor">Trading Advisor</MenuItem>
                  <MenuItem value="market_analyst">Market Analyst</MenuItem>
                  <MenuItem value="risk_manager">Risk Manager</MenuItem>
                  <MenuItem value="portfolio_optimizer">Portfolio Optimizer</MenuItem>
                  <MenuItem value="news_analyst">News Analyst</MenuItem>
                  <MenuItem value="technical_analyst">Technical Analyst</MenuItem>
                  <MenuItem value="sentiment_analyzer">Sentiment Analyzer</MenuItem>
                  <MenuItem value="blockchain_monitor">Blockchain Monitor</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>LLM Provider</InputLabel>
                <Select
                  value={newAgent.llm_provider}
                  onChange={(e) => setNewAgent({ ...newAgent, llm_provider: e.target.value })}
                >
                  <MenuItem value="openai_gpt4">OpenAI GPT-4</MenuItem>
                  <MenuItem value="openai_gpt35">OpenAI GPT-3.5</MenuItem>
                  <MenuItem value="claude_3">Anthropic Claude 3</MenuItem>
                  <MenuItem value="gemini_pro">Google Gemini Pro</MenuItem>
                  <MenuItem value="deepseek">DeepSeek</MenuItem>
                  <MenuItem value="local_llama">Local LLaMA</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Model Name"
                value={newAgent.model_name}
                onChange={(e) => setNewAgent({ ...newAgent, model_name: e.target.value })}
              />
            </Grid>
            <Grid item xs={12}>
              <FormControlLabel
                control={
                  <Switch
                    checked={newAgent.enabled}
                    onChange={(e) => setNewAgent({ ...newAgent, enabled: e.target.checked })}
                  />
                }
                label="Enabled"
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setAddAgentOpen(false)}>Cancel</Button>
          <Button onClick={addAgent} variant="contained">Add Agent</Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default AIAgentDashboard;
