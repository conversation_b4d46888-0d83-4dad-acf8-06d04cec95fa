import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Grid,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  LinearProgress,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Tooltip,
  Alert,
  Tabs,
  Tab,
  CircularProgress,
  Badge
} from '@mui/material';
import {
  PlayArrow as StartIcon,
  Stop as StopIcon,
  Refresh as RefreshIcon,
  Add as AddIcon,
  Visibility as ViewIcon,
  Cancel as CancelIcon,
  Assessment as MetricsIcon,
  Timeline as TimelineIcon,
  Security as SecurityIcon,
  Psychology as AIIcon,
  TrendingUp as TradingIcon,
  Science as QuantumIcon,
  Gavel as ComplianceIcon,
  Analytics as AnalyticsIcon
} from '@mui/icons-material';
import { styled } from '@mui/material/styles';

// Styled components
const MetricCard = styled(Card)(({ theme }) => ({
  background: `linear-gradient(135deg, ${theme.palette.primary.main}20, ${theme.palette.secondary.main}20)`,
  border: `1px solid ${theme.palette.primary.main}30`,
  height: '100%'
}));

const StatusChip = styled(Chip)<{ status: string }>(({ theme, status }) => {
  const getColor = () => {
    switch (status) {
      case 'running': return theme.palette.info.main;
      case 'completed': return theme.palette.success.main;
      case 'failed': return theme.palette.error.main;
      case 'cancelled': return theme.palette.warning.main;
      default: return theme.palette.grey[500];
    }
  };
  
  return {
    backgroundColor: getColor(),
    color: theme.palette.common.white
  };
});

// Types
interface Workflow {
  workflow_id: string;
  status: string;
  progress: number;
  created_at: string;
  started_at?: string;
  completed_at?: string;
  task_count: number;
  completed_tasks: number;
  failed_tasks: number;
}

interface WorkflowMetrics {
  total_workflows: number;
  running_workflows: number;
  completed_workflows: number;
  failed_workflows: number;
  success_rate: number;
  healthy_services: number;
  total_services: number;
}

interface ServiceHealth {
  services: Record<string, {
    status: string;
    last_check: string;
    error?: string;
  }>;
  total_services: number;
  healthy_services: number;
}

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const TabPanel: React.FC<TabPanelProps> = ({ children, value, index }) => (
  <div hidden={value !== index}>
    {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
  </div>
);

const WorkflowDashboard: React.FC = () => {
  const [workflows, setWorkflows] = useState<Workflow[]>([]);
  const [metrics, setMetrics] = useState<WorkflowMetrics | null>(null);
  const [serviceHealth, setServiceHealth] = useState<ServiceHealth | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [tabValue, setTabValue] = useState(0);
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [newWorkflow, setNewWorkflow] = useState({
    workflow_type: '',
    name: '',
    description: '',
    metadata: {}
  });

  const workflowTypes = [
    { value: 'trading', label: 'Trading', icon: <TradingIcon />, color: 'primary' },
    { value: 'ai_analysis', label: 'AI Analysis', icon: <AIIcon />, color: 'secondary' },
    { value: 'quantum_computation', label: 'Quantum', icon: <QuantumIcon />, color: 'info' },
    { value: 'compliance_check', label: 'Compliance', icon: <ComplianceIcon />, color: 'warning' },
    { value: 'cybersecurity_scan', label: 'Security', icon: <SecurityIcon />, color: 'error' },
    { value: 'risk_management', label: 'Risk Management', icon: <AnalyticsIcon />, color: 'success' }
  ];

  useEffect(() => {
    loadData();
    const interval = setInterval(loadData, 10000); // Refresh every 10 seconds
    return () => clearInterval(interval);
  }, []);

  const loadData = async () => {
    setLoading(true);
    try {
      await Promise.all([
        loadWorkflows(),
        loadMetrics(),
        loadServiceHealth()
      ]);
    } catch (error) {
      console.error('Failed to load data:', error);
      setError('Failed to load dashboard data');
    } finally {
      setLoading(false);
    }
  };

  const loadWorkflows = async () => {
    try {
      const response = await fetch('http://localhost:8100/workflows');
      if (response.ok) {
        const data = await response.json();
        setWorkflows(data);
      }
    } catch (error) {
      console.error('Failed to load workflows:', error);
    }
  };

  const loadMetrics = async () => {
    try {
      const response = await fetch('http://localhost:8100/metrics');
      if (response.ok) {
        const data = await response.json();
        setMetrics(data);
      }
    } catch (error) {
      console.error('Failed to load metrics:', error);
    }
  };

  const loadServiceHealth = async () => {
    try {
      const response = await fetch('http://localhost:8100/services/health');
      if (response.ok) {
        const data = await response.json();
        setServiceHealth(data);
      }
    } catch (error) {
      console.error('Failed to load service health:', error);
    }
  };

  const createWorkflow = async () => {
    try {
      const response = await fetch('http://localhost:8100/workflows', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(newWorkflow)
      });

      if (response.ok) {
        setCreateDialogOpen(false);
        setNewWorkflow({
          workflow_type: '',
          name: '',
          description: '',
          metadata: {}
        });
        await loadData();
      } else {
        const errorData = await response.json();
        setError(errorData.detail || 'Failed to create workflow');
      }
    } catch (error) {
      console.error('Failed to create workflow:', error);
      setError('Failed to create workflow');
    }
  };

  const cancelWorkflow = async (workflowId: string) => {
    try {
      const response = await fetch(`http://localhost:8100/workflows/${workflowId}/cancel`, {
        method: 'POST'
      });

      if (response.ok) {
        await loadData();
      }
    } catch (error) {
      console.error('Failed to cancel workflow:', error);
    }
  };

  const runDemo = async () => {
    setLoading(true);
    try {
      const response = await fetch('http://localhost:8100/demo', {
        method: 'POST'
      });

      if (response.ok) {
        await loadData();
      }
    } catch (error) {
      console.error('Demo failed:', error);
      setError('Demo failed');
    } finally {
      setLoading(false);
    }
  };

  const getWorkflowIcon = (workflowType: string) => {
    const type = workflowTypes.find(t => t.value === workflowType);
    return type ? type.icon : <TimelineIcon />;
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'running': return 'info';
      case 'completed': return 'success';
      case 'failed': return 'error';
      case 'cancelled': return 'warning';
      default: return 'default';
    }
  };

  return (
    <Box sx={{ p: 3 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" component="h1">
          🔄 Workflow Dashboard
        </Typography>
        <Box sx={{ display: 'flex', gap: 1 }}>
          <Button
            variant="outlined"
            startIcon={<RefreshIcon />}
            onClick={loadData}
            disabled={loading}
          >
            Refresh
          </Button>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={() => setCreateDialogOpen(true)}
          >
            Create Workflow
          </Button>
          <Button
            variant="contained"
            color="secondary"
            onClick={runDemo}
            disabled={loading}
          >
            Run Demo
          </Button>
        </Box>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      {/* Metrics Overview */}
      {metrics && (
        <Grid container spacing={3} sx={{ mb: 3 }}>
          <Grid item xs={12} sm={6} md={2}>
            <MetricCard>
              <CardContent>
                <Typography variant="h6" color="primary">
                  {metrics.total_workflows}
                </Typography>
                <Typography variant="body2" color="textSecondary">
                  Total Workflows
                </Typography>
              </CardContent>
            </MetricCard>
          </Grid>
          <Grid item xs={12} sm={6} md={2}>
            <MetricCard>
              <CardContent>
                <Badge badgeContent={metrics.running_workflows} color="info">
                  <Typography variant="h6" color="primary">
                    {metrics.running_workflows}
                  </Typography>
                </Badge>
                <Typography variant="body2" color="textSecondary">
                  Running
                </Typography>
              </CardContent>
            </MetricCard>
          </Grid>
          <Grid item xs={12} sm={6} md={2}>
            <MetricCard>
              <CardContent>
                <Typography variant="h6" color="success.main">
                  {metrics.completed_workflows}
                </Typography>
                <Typography variant="body2" color="textSecondary">
                  Completed
                </Typography>
              </CardContent>
            </MetricCard>
          </Grid>
          <Grid item xs={12} sm={6} md={2}>
            <MetricCard>
              <CardContent>
                <Typography variant="h6" color="error.main">
                  {metrics.failed_workflows}
                </Typography>
                <Typography variant="body2" color="textSecondary">
                  Failed
                </Typography>
              </CardContent>
            </MetricCard>
          </Grid>
          <Grid item xs={12} sm={6} md={2}>
            <MetricCard>
              <CardContent>
                <Typography variant="h6" color="primary">
                  {metrics.success_rate.toFixed(1)}%
                </Typography>
                <Typography variant="body2" color="textSecondary">
                  Success Rate
                </Typography>
              </CardContent>
            </MetricCard>
          </Grid>
          <Grid item xs={12} sm={6} md={2}>
            <MetricCard>
              <CardContent>
                <Typography variant="h6" color="primary">
                  {metrics.healthy_services}/{metrics.total_services}
                </Typography>
                <Typography variant="body2" color="textSecondary">
                  Services
                </Typography>
              </CardContent>
            </MetricCard>
          </Grid>
        </Grid>
      )}

      {/* Tabs */}
      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
        <Tabs value={tabValue} onChange={(_, newValue) => setTabValue(newValue)}>
          <Tab label="Active Workflows" icon={<TimelineIcon />} />
          <Tab label="Service Health" icon={<MetricsIcon />} />
          <Tab label="Analytics" icon={<AnalyticsIcon />} />
        </Tabs>
      </Box>

      {/* Active Workflows Tab */}
      <TabPanel value={tabValue} index={0}>
        <TableContainer component={Paper}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Workflow</TableCell>
                <TableCell>Status</TableCell>
                <TableCell>Progress</TableCell>
                <TableCell>Tasks</TableCell>
                <TableCell>Created</TableCell>
                <TableCell>Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {workflows.map((workflow) => (
                <TableRow key={workflow.workflow_id}>
                  <TableCell>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      {getWorkflowIcon(workflow.workflow_id.split('_')[2] || '')}
                      <Box>
                        <Typography variant="body2" fontWeight="bold">
                          {workflow.workflow_id}
                        </Typography>
                        <Typography variant="caption" color="textSecondary">
                          ID: {workflow.workflow_id.split('_').slice(-2).join('_')}
                        </Typography>
                      </Box>
                    </Box>
                  </TableCell>
                  <TableCell>
                    <StatusChip
                      label={workflow.status}
                      status={workflow.status}
                      size="small"
                    />
                  </TableCell>
                  <TableCell>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <LinearProgress
                        variant="determinate"
                        value={workflow.progress}
                        sx={{ width: 100, height: 6, borderRadius: 3 }}
                      />
                      <Typography variant="body2">
                        {workflow.progress.toFixed(0)}%
                      </Typography>
                    </Box>
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2">
                      {workflow.completed_tasks}/{workflow.task_count}
                      {workflow.failed_tasks > 0 && (
                        <Chip
                          label={`${workflow.failed_tasks} failed`}
                          size="small"
                          color="error"
                          sx={{ ml: 1 }}
                        />
                      )}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2">
                      {new Date(workflow.created_at).toLocaleString()}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Box sx={{ display: 'flex', gap: 0.5 }}>
                      <Tooltip title="View Details">
                        <IconButton size="small">
                          <ViewIcon />
                        </IconButton>
                      </Tooltip>
                      {workflow.status === 'running' && (
                        <Tooltip title="Cancel">
                          <IconButton
                            size="small"
                            onClick={() => cancelWorkflow(workflow.workflow_id)}
                          >
                            <CancelIcon />
                          </IconButton>
                        </Tooltip>
                      )}
                    </Box>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </TabPanel>

      {/* Service Health Tab */}
      <TabPanel value={tabValue} index={1}>
        {serviceHealth && (
          <Grid container spacing={3}>
            {Object.entries(serviceHealth.services).map(([serviceName, health]) => (
              <Grid item xs={12} md={6} key={serviceName}>
                <Card>
                  <CardContent>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <Typography variant="h6">
                        {serviceName.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                      </Typography>
                      <Chip
                        label={health.status}
                        color={health.status === 'healthy' ? 'success' : 'error'}
                        size="small"
                      />
                    </Box>
                    <Typography variant="body2" color="textSecondary">
                      Last Check: {new Date(health.last_check).toLocaleString()}
                    </Typography>
                    {health.error && (
                      <Typography variant="body2" color="error" sx={{ mt: 1 }}>
                        Error: {health.error}
                      </Typography>
                    )}
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        )}
      </TabPanel>

      {/* Analytics Tab */}
      <TabPanel value={tabValue} index={2}>
        <Grid container spacing={3}>
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Workflow Types Distribution
                </Typography>
                {workflowTypes.map((type) => {
                  const count = workflows.filter(w => 
                    w.workflow_id.includes(type.value)
                  ).length;
                  const percentage = workflows.length > 0 ? (count / workflows.length) * 100 : 0;
                  
                  return (
                    <Box key={type.value} sx={{ mb: 2 }}>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          {type.icon}
                          <Typography variant="body2">{type.label}</Typography>
                        </Box>
                        <Typography variant="body2">{count}</Typography>
                      </Box>
                      <LinearProgress
                        variant="determinate"
                        value={percentage}
                        sx={{ height: 6, borderRadius: 3 }}
                      />
                    </Box>
                  );
                })}
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  System Performance
                </Typography>
                <Box sx={{ mb: 2 }}>
                  <Typography variant="body2" gutterBottom>
                    Overall Success Rate
                  </Typography>
                  <LinearProgress
                    variant="determinate"
                    value={metrics?.success_rate || 0}
                    color="success"
                    sx={{ height: 8, borderRadius: 4 }}
                  />
                  <Typography variant="caption" color="textSecondary">
                    {metrics?.success_rate.toFixed(1)}%
                  </Typography>
                </Box>
                <Box sx={{ mb: 2 }}>
                  <Typography variant="body2" gutterBottom>
                    Service Health
                  </Typography>
                  <LinearProgress
                    variant="determinate"
                    value={serviceHealth ? (serviceHealth.healthy_services / serviceHealth.total_services) * 100 : 0}
                    color="info"
                    sx={{ height: 8, borderRadius: 4 }}
                  />
                  <Typography variant="caption" color="textSecondary">
                    {serviceHealth?.healthy_services}/{serviceHealth?.total_services} services healthy
                  </Typography>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </TabPanel>

      {/* Create Workflow Dialog */}
      <Dialog open={createDialogOpen} onClose={() => setCreateDialogOpen(false)} maxWidth="md" fullWidth>
        <DialogTitle>Create New Workflow</DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12}>
              <FormControl fullWidth>
                <InputLabel>Workflow Type</InputLabel>
                <Select
                  value={newWorkflow.workflow_type}
                  onChange={(e) => setNewWorkflow({ ...newWorkflow, workflow_type: e.target.value })}
                >
                  {workflowTypes.map((type) => (
                    <MenuItem key={type.value} value={type.value}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        {type.icon}
                        {type.label}
                      </Box>
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Workflow Name"
                value={newWorkflow.name}
                onChange={(e) => setNewWorkflow({ ...newWorkflow, name: e.target.value })}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                multiline
                rows={3}
                label="Description"
                value={newWorkflow.description}
                onChange={(e) => setNewWorkflow({ ...newWorkflow, description: e.target.value })}
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setCreateDialogOpen(false)}>Cancel</Button>
          <Button onClick={createWorkflow} variant="contained">Create Workflow</Button>
        </DialogActions>
      </Dialog>

      {loading && (
        <Box sx={{ display: 'flex', justifyContent: 'center', mt: 3 }}>
          <CircularProgress />
        </Box>
      )}
    </Box>
  );
};

export default WorkflowDashboard;
