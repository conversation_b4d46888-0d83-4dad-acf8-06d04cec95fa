import React, { useState, useCallback } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  Grid,
  Paper,
  Stepper,
  Step,
  StepLabel,
  StepContent,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  IconButton,
  Tooltip
} from '@mui/material';
import {
  Add as AddIcon,
  Delete as DeleteIcon,
  PlayArrow as PlayIcon,
  Save as SaveIcon,
  ExpandMore as ExpandMoreIcon,
  DragIndicator as DragIcon,
  TrendingUp as TradingIcon,
  Psychology as AIIcon,
  Science as QuantumIcon,
  Security as SecurityIcon,
  Gavel as ComplianceIcon,
  Analytics as AnalyticsIcon,
  AccountBalance as PortfolioIcon,
  Biotech as GenomicIcon
} from '@mui/icons-material';
import { styled } from '@mui/material/styles';

// Styled components
const WorkflowCanvas = styled(Paper)(({ theme }) => ({
  minHeight: '600px',
  padding: theme.spacing(2),
  backgroundColor: theme.palette.grey[50],
  border: `2px dashed ${theme.palette.grey[300]}`,
  borderRadius: theme.shape.borderRadius
}));

const TaskCard = styled(Card)(({ theme }) => ({
  margin: theme.spacing(1),
  cursor: 'grab',
  '&:hover': {
    boxShadow: theme.shadows[4]
  }
}));

const StepCard = styled(Card)(({ theme }) => ({
  marginBottom: theme.spacing(2),
  border: `1px solid ${theme.palette.primary.main}30`
}));

// Types
interface WorkflowStep {
  id: string;
  type: string;
  name: string;
  description: string;
  parameters: Record<string, any>;
  dependencies: string[];
  position: { x: number; y: number };
}

interface WorkflowTemplate {
  id: string;
  name: string;
  description: string;
  type: string;
  steps: WorkflowStep[];
  metadata: Record<string, any>;
}

const WorkflowBuilder: React.FC = () => {
  const [workflow, setWorkflow] = useState<WorkflowTemplate>({
    id: '',
    name: '',
    description: '',
    type: '',
    steps: [],
    metadata: {}
  });
  
  const [selectedStep, setSelectedStep] = useState<WorkflowStep | null>(null);
  const [stepDialogOpen, setStepDialogOpen] = useState(false);
  const [saveDialogOpen, setSaveDialogOpen] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  const stepTypes = [
    {
      type: 'market_analysis',
      name: 'Market Analysis',
      icon: <TradingIcon />,
      category: 'Trading',
      description: 'Analyze market conditions and trends',
      parameters: ['symbols', 'timeframe', 'indicators']
    },
    {
      type: 'ai_consensus',
      name: 'AI Consensus',
      icon: <AIIcon />,
      category: 'AI',
      description: 'Get AI consensus from multiple models',
      parameters: ['query', 'models', 'confidence_threshold']
    },
    {
      type: 'risk_assessment',
      name: 'Risk Assessment',
      icon: <SecurityIcon />,
      category: 'Risk',
      description: 'Assess portfolio and trading risks',
      parameters: ['portfolio', 'risk_metrics', 'thresholds']
    },
    {
      type: 'quantum_computation',
      name: 'Quantum Computation',
      icon: <QuantumIcon />,
      category: 'Quantum',
      description: 'Execute quantum algorithms',
      parameters: ['algorithm', 'qubits', 'backend']
    },
    {
      type: 'compliance_check',
      name: 'Compliance Check',
      icon: <ComplianceIcon />,
      category: 'Compliance',
      description: 'Verify regulatory compliance',
      parameters: ['user_id', 'check_type', 'jurisdiction']
    },
    {
      type: 'portfolio_optimization',
      name: 'Portfolio Optimization',
      icon: <PortfolioIcon />,
      category: 'Portfolio',
      description: 'Optimize portfolio allocation',
      parameters: ['assets', 'constraints', 'objective']
    },
    {
      type: 'genomic_analysis',
      name: 'Genomic Analysis',
      icon: <GenomicIcon />,
      category: 'Genomics',
      description: 'Analyze genomic data',
      parameters: ['data_source', 'analysis_type', 'privacy_level']
    },
    {
      type: 'security_scan',
      name: 'Security Scan',
      icon: <SecurityIcon />,
      category: 'Security',
      description: 'Perform security vulnerability scan',
      parameters: ['targets', 'scan_type', 'depth']
    }
  ];

  const workflowTemplates = [
    {
      name: 'Automated Trading',
      description: 'Complete automated trading workflow',
      type: 'trading',
      steps: ['market_analysis', 'ai_consensus', 'risk_assessment', 'trade_execution']
    },
    {
      name: 'AI-Powered Analysis',
      description: 'Comprehensive AI analysis workflow',
      type: 'ai_analysis',
      steps: ['data_collection', 'ai_consensus', 'result_validation', 'report_generation']
    },
    {
      name: 'Quantum Research',
      description: 'Quantum computation research workflow',
      type: 'quantum_computation',
      steps: ['quantum_preparation', 'quantum_execution', 'result_analysis', 'optimization']
    },
    {
      name: 'Compliance Verification',
      description: 'Complete compliance check workflow',
      type: 'compliance_check',
      steps: ['kyc_verification', 'aml_screening', 'risk_scoring', 'compliance_report']
    }
  ];

  const addStep = (stepType: string) => {
    const stepTemplate = stepTypes.find(t => t.type === stepType);
    if (!stepTemplate) return;

    const newStep: WorkflowStep = {
      id: `step_${Date.now()}`,
      type: stepType,
      name: stepTemplate.name,
      description: stepTemplate.description,
      parameters: {},
      dependencies: [],
      position: { x: 100, y: workflow.steps.length * 120 + 100 }
    };

    setWorkflow(prev => ({
      ...prev,
      steps: [...prev.steps, newStep]
    }));
  };

  const removeStep = (stepId: string) => {
    setWorkflow(prev => ({
      ...prev,
      steps: prev.steps.filter(step => step.id !== stepId)
    }));
  };

  const updateStep = (stepId: string, updates: Partial<WorkflowStep>) => {
    setWorkflow(prev => ({
      ...prev,
      steps: prev.steps.map(step => 
        step.id === stepId ? { ...step, ...updates } : step
      )
    }));
  };

  const loadTemplate = (templateName: string) => {
    const template = workflowTemplates.find(t => t.name === templateName);
    if (!template) return;

    const steps: WorkflowStep[] = template.steps.map((stepType, index) => {
      const stepTemplate = stepTypes.find(t => t.type === stepType);
      return {
        id: `step_${Date.now()}_${index}`,
        type: stepType,
        name: stepTemplate?.name || stepType,
        description: stepTemplate?.description || '',
        parameters: {},
        dependencies: index > 0 ? [`step_${Date.now()}_${index - 1}`] : [],
        position: { x: 100, y: index * 120 + 100 }
      };
    });

    setWorkflow({
      id: `workflow_${Date.now()}`,
      name: template.name,
      description: template.description,
      type: template.type,
      steps,
      metadata: {}
    });
  };

  const saveWorkflow = async () => {
    if (!workflow.name || workflow.steps.length === 0) {
      setError('Please provide a workflow name and add at least one step');
      return;
    }

    try {
      const workflowData = {
        workflow_type: workflow.type,
        name: workflow.name,
        description: workflow.description,
        metadata: {
          ...workflow.metadata,
          steps: workflow.steps,
          created_by: 'workflow_builder',
          template: true
        }
      };

      const response = await fetch('http://localhost:8100/workflows', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(workflowData)
      });

      if (response.ok) {
        const result = await response.json();
        setSuccess(`Workflow created successfully: ${result.workflow_id}`);
        setSaveDialogOpen(false);
      } else {
        const errorData = await response.json();
        setError(errorData.detail || 'Failed to save workflow');
      }
    } catch (error) {
      console.error('Failed to save workflow:', error);
      setError('Failed to save workflow');
    }
  };

  const executeWorkflow = async () => {
    if (!workflow.name || workflow.steps.length === 0) {
      setError('Please create a workflow before executing');
      return;
    }

    await saveWorkflow();
  };

  return (
    <Box sx={{ p: 3 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" component="h1">
          🔧 Workflow Builder
        </Typography>
        <Box sx={{ display: 'flex', gap: 1 }}>
          <Button
            variant="outlined"
            startIcon={<SaveIcon />}
            onClick={() => setSaveDialogOpen(true)}
            disabled={workflow.steps.length === 0}
          >
            Save
          </Button>
          <Button
            variant="contained"
            startIcon={<PlayIcon />}
            onClick={executeWorkflow}
            disabled={workflow.steps.length === 0}
          >
            Execute
          </Button>
        </Box>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      {success && (
        <Alert severity="success" sx={{ mb: 3 }} onClose={() => setSuccess(null)}>
          {success}
        </Alert>
      )}

      <Grid container spacing={3}>
        {/* Workflow Configuration */}
        <Grid item xs={12} md={4}>
          <Card sx={{ mb: 2 }}>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Workflow Configuration
              </Typography>
              <TextField
                fullWidth
                label="Workflow Name"
                value={workflow.name}
                onChange={(e) => setWorkflow(prev => ({ ...prev, name: e.target.value }))}
                sx={{ mb: 2 }}
              />
              <TextField
                fullWidth
                multiline
                rows={3}
                label="Description"
                value={workflow.description}
                onChange={(e) => setWorkflow(prev => ({ ...prev, description: e.target.value }))}
                sx={{ mb: 2 }}
              />
              <FormControl fullWidth>
                <InputLabel>Workflow Type</InputLabel>
                <Select
                  value={workflow.type}
                  onChange={(e) => setWorkflow(prev => ({ ...prev, type: e.target.value }))}
                >
                  <MenuItem value="trading">Trading</MenuItem>
                  <MenuItem value="ai_analysis">AI Analysis</MenuItem>
                  <MenuItem value="quantum_computation">Quantum Computation</MenuItem>
                  <MenuItem value="compliance_check">Compliance Check</MenuItem>
                  <MenuItem value="cybersecurity_scan">Security Scan</MenuItem>
                  <MenuItem value="risk_management">Risk Management</MenuItem>
                </Select>
              </FormControl>
            </CardContent>
          </Card>

          {/* Templates */}
          <Card sx={{ mb: 2 }}>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Quick Templates
              </Typography>
              {workflowTemplates.map((template) => (
                <Button
                  key={template.name}
                  fullWidth
                  variant="outlined"
                  onClick={() => loadTemplate(template.name)}
                  sx={{ mb: 1 }}
                >
                  {template.name}
                </Button>
              ))}
            </CardContent>
          </Card>

          {/* Step Library */}
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Step Library
              </Typography>
              {Object.entries(
                stepTypes.reduce((acc, step) => {
                  if (!acc[step.category]) acc[step.category] = [];
                  acc[step.category].push(step);
                  return acc;
                }, {} as Record<string, typeof stepTypes>)
              ).map(([category, steps]) => (
                <Accordion key={category}>
                  <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                    <Typography variant="subtitle2">{category}</Typography>
                  </AccordionSummary>
                  <AccordionDetails>
                    <List dense>
                      {steps.map((step) => (
                        <ListItem
                          key={step.type}
                          button
                          onClick={() => addStep(step.type)}
                        >
                          <ListItemIcon>{step.icon}</ListItemIcon>
                          <ListItemText
                            primary={step.name}
                            secondary={step.description}
                          />
                        </ListItem>
                      ))}
                    </List>
                  </AccordionDetails>
                </Accordion>
              ))}
            </CardContent>
          </Card>
        </Grid>

        {/* Workflow Canvas */}
        <Grid item xs={12} md={8}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Workflow Canvas
              </Typography>
              <WorkflowCanvas>
                {workflow.steps.length === 0 ? (
                  <Box
                    sx={{
                      display: 'flex',
                      flexDirection: 'column',
                      alignItems: 'center',
                      justifyContent: 'center',
                      height: '400px',
                      color: 'text.secondary'
                    }}
                  >
                    <Typography variant="h6" gutterBottom>
                      Start Building Your Workflow
                    </Typography>
                    <Typography variant="body2">
                      Add steps from the library or use a template
                    </Typography>
                  </Box>
                ) : (
                  <Stepper orientation="vertical">
                    {workflow.steps.map((step, index) => (
                      <Step key={step.id} active={true}>
                        <StepLabel>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            {stepTypes.find(t => t.type === step.type)?.icon}
                            {step.name}
                          </Box>
                        </StepLabel>
                        <StepContent>
                          <StepCard>
                            <CardContent>
                              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                                <Typography variant="body2" color="textSecondary">
                                  {step.description}
                                </Typography>
                                <Box>
                                  <Tooltip title="Configure">
                                    <IconButton
                                      size="small"
                                      onClick={() => {
                                        setSelectedStep(step);
                                        setStepDialogOpen(true);
                                      }}
                                    >
                                      <DragIcon />
                                    </IconButton>
                                  </Tooltip>
                                  <Tooltip title="Remove">
                                    <IconButton
                                      size="small"
                                      onClick={() => removeStep(step.id)}
                                    >
                                      <DeleteIcon />
                                    </IconButton>
                                  </Tooltip>
                                </Box>
                              </Box>
                              <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                                <Chip label={`Type: ${step.type}`} size="small" />
                                {step.dependencies.length > 0 && (
                                  <Chip
                                    label={`Dependencies: ${step.dependencies.length}`}
                                    size="small"
                                    color="secondary"
                                  />
                                )}
                                {Object.keys(step.parameters).length > 0 && (
                                  <Chip
                                    label={`Parameters: ${Object.keys(step.parameters).length}`}
                                    size="small"
                                    color="primary"
                                  />
                                )}
                              </Box>
                            </CardContent>
                          </StepCard>
                        </StepContent>
                      </Step>
                    ))}
                  </Stepper>
                )}
              </WorkflowCanvas>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Step Configuration Dialog */}
      <Dialog open={stepDialogOpen} onClose={() => setStepDialogOpen(false)} maxWidth="md" fullWidth>
        <DialogTitle>Configure Step: {selectedStep?.name}</DialogTitle>
        <DialogContent>
          {selectedStep && (
            <Grid container spacing={2} sx={{ mt: 1 }}>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Step Name"
                  value={selectedStep.name}
                  onChange={(e) => updateStep(selectedStep.id, { name: e.target.value })}
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  multiline
                  rows={2}
                  label="Description"
                  value={selectedStep.description}
                  onChange={(e) => updateStep(selectedStep.id, { description: e.target.value })}
                />
              </Grid>
              <Grid item xs={12}>
                <Typography variant="subtitle2" gutterBottom>
                  Parameters
                </Typography>
                {stepTypes.find(t => t.type === selectedStep.type)?.parameters.map((param) => (
                  <TextField
                    key={param}
                    fullWidth
                    label={param.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                    value={selectedStep.parameters[param] || ''}
                    onChange={(e) => updateStep(selectedStep.id, {
                      parameters: { ...selectedStep.parameters, [param]: e.target.value }
                    })}
                    sx={{ mb: 1 }}
                  />
                ))}
              </Grid>
            </Grid>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setStepDialogOpen(false)}>Cancel</Button>
          <Button onClick={() => setStepDialogOpen(false)} variant="contained">Save</Button>
        </DialogActions>
      </Dialog>

      {/* Save Workflow Dialog */}
      <Dialog open={saveDialogOpen} onClose={() => setSaveDialogOpen(false)}>
        <DialogTitle>Save Workflow</DialogTitle>
        <DialogContent>
          <Typography variant="body2" gutterBottom>
            Are you sure you want to save and execute this workflow?
          </Typography>
          <Typography variant="body2" color="textSecondary">
            Workflow: {workflow.name}
          </Typography>
          <Typography variant="body2" color="textSecondary">
            Steps: {workflow.steps.length}
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setSaveDialogOpen(false)}>Cancel</Button>
          <Button onClick={saveWorkflow} variant="contained">Save & Execute</Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default WorkflowBuilder;
