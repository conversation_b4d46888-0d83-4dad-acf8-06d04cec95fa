"""
Kontour Coin Complete Backend Workflow Orchestrator
Comprehensive backend workflow system integrating all platform components
"""

import asyncio
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass, asdict
from enum import Enum
import redis
from kafka import KafkaProducer, KafkaConsumer
import httpx
import websockets
import numpy as np
from concurrent.futures import ThreadPoolExecutor
import os
from dotenv import load_dotenv

load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class WorkflowType(Enum):
    TRADING = "trading"
    RISK_MANAGEMENT = "risk_management"
    PORTFOLIO_OPTIMIZATION = "portfolio_optimization"
    AI_ANALYSIS = "ai_analysis"
    QUANTUM_COMPUTATION = "quantum_computation"
    BLOCKCHAIN_VALIDATION = "blockchain_validation"
    COMPLIANCE_CHECK = "compliance_check"
    MARKET_ANALYSIS = "market_analysis"
    GENOMIC_PROCESSING = "genomic_processing"
    CYBERSECURITY_SCAN = "cybersecurity_scan"

class WorkflowStatus(Enum):
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    PAUSED = "paused"

@dataclass
class WorkflowTask:
    """Individual workflow task"""
    task_id: str
    workflow_id: str
    task_type: str
    service_endpoint: str
    payload: Dict[str, Any]
    dependencies: List[str]
    status: WorkflowStatus
    priority: int
    created_at: datetime
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    result: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    retry_count: int = 0
    max_retries: int = 3

@dataclass
class Workflow:
    """Complete workflow definition"""
    workflow_id: str
    workflow_type: WorkflowType
    name: str
    description: str
    tasks: List[WorkflowTask]
    status: WorkflowStatus
    created_by: str
    created_at: datetime
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    metadata: Dict[str, Any] = None
    progress: float = 0.0
    estimated_duration: Optional[int] = None

class KontourBackendOrchestrator:
    """Complete backend workflow orchestrator"""
    
    def __init__(self):
        self.workflows: Dict[str, Workflow] = {}
        self.active_tasks: Dict[str, WorkflowTask] = {}
        self.service_registry = {}
        self.executor = ThreadPoolExecutor(max_workers=20)
        
        # Platform connections
        self.redis_client = None
        self.kafka_producer = None
        self.kafka_consumer = None
        self.websocket_connections = set()
        
        # Service endpoints
        self.service_endpoints = {
            'ai_agent': 'http://localhost:8025',
            'trading_engine': 'http://localhost:8085',
            'risk_management': 'http://localhost:8087',
            'portfolio_service': 'http://localhost:8088',
            'blockchain_service': 'http://localhost:8089',
            'quantum_service': 'http://localhost:8090',
            'compliance_service': 'http://localhost:8091',
            'market_data': 'http://localhost:8086',
            'genomic_service': 'http://localhost:8092',
            'cybersecurity_service': 'http://localhost:8093',
            'chainlink_service': 'http://localhost:8095'
        }
        
        self._initialize_connections()
        
    def _initialize_connections(self):
        """Initialize platform connections"""
        try:
            # Redis connection
            self.redis_client = redis.Redis(
                host=os.getenv('REDIS_HOST', 'localhost'),
                port=int(os.getenv('REDIS_PORT', 6379)),
                decode_responses=True
            )
            
            # Kafka producer
            self.kafka_producer = KafkaProducer(
                bootstrap_servers=[os.getenv('KAFKA_BOOTSTRAP_SERVERS', 'localhost:9092')],
                value_serializer=lambda x: json.dumps(x, default=str).encode('utf-8')
            )
            
            # Kafka consumer
            self.kafka_consumer = KafkaConsumer(
                'workflow_events',
                'trading_events',
                'market_events',
                'ai_events',
                'quantum_events',
                'compliance_events',
                bootstrap_servers=[os.getenv('KAFKA_BOOTSTRAP_SERVERS', 'localhost:9092')],
                value_deserializer=lambda x: json.loads(x.decode('utf-8')),
                group_id='backend_orchestrator_group'
            )
            
            logger.info("✅ Backend orchestrator connections initialized")
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize connections: {e}")
            raise
    
    async def start_orchestrator(self):
        """Start the complete backend orchestrator"""
        logger.info("🚀 Starting Kontour Backend Orchestrator...")
        
        # Start orchestrator tasks
        tasks = [
            asyncio.create_task(self._process_workflow_queue()),
            asyncio.create_task(self._monitor_active_workflows()),
            asyncio.create_task(self._process_kafka_events()),
            asyncio.create_task(self._health_check_services()),
            asyncio.create_task(self._websocket_server()),
            asyncio.create_task(self._periodic_cleanup())
        ]
        
        try:
            await asyncio.gather(*tasks)
        except Exception as e:
            logger.error(f"Orchestrator error: {e}")
            raise
    
    async def create_workflow(self, workflow_type: WorkflowType, name: str, 
                            description: str, created_by: str, 
                            metadata: Dict[str, Any] = None) -> str:
        """Create a new workflow"""
        
        workflow_id = f"wf_{int(datetime.now().timestamp())}_{workflow_type.value}"
        
        # Generate tasks based on workflow type
        tasks = await self._generate_workflow_tasks(workflow_type, workflow_id, metadata or {})
        
        workflow = Workflow(
            workflow_id=workflow_id,
            workflow_type=workflow_type,
            name=name,
            description=description,
            tasks=tasks,
            status=WorkflowStatus.PENDING,
            created_by=created_by,
            created_at=datetime.now(),
            metadata=metadata
        )
        
        self.workflows[workflow_id] = workflow
        
        # Store in Redis
        await self._store_workflow(workflow)
        
        # Emit event
        await self._emit_workflow_event('workflow_created', workflow_id, {
            'workflow_type': workflow_type.value,
            'name': name,
            'task_count': len(tasks)
        })
        
        logger.info(f"✅ Created workflow: {workflow_id}")
        return workflow_id
    
    async def _generate_workflow_tasks(self, workflow_type: WorkflowType, 
                                     workflow_id: str, metadata: Dict[str, Any]) -> List[WorkflowTask]:
        """Generate tasks based on workflow type"""
        
        tasks = []
        base_time = datetime.now()
        
        if workflow_type == WorkflowType.TRADING:
            tasks = [
                WorkflowTask(
                    task_id=f"{workflow_id}_market_analysis",
                    workflow_id=workflow_id,
                    task_type="market_analysis",
                    service_endpoint=self.service_endpoints['market_data'],
                    payload={"action": "analyze_market", "symbols": metadata.get("symbols", ["BTC", "ETH"])},
                    dependencies=[],
                    status=WorkflowStatus.PENDING,
                    priority=1,
                    created_at=base_time
                ),
                WorkflowTask(
                    task_id=f"{workflow_id}_ai_recommendation",
                    workflow_id=workflow_id,
                    task_type="ai_analysis",
                    service_endpoint=self.service_endpoints['ai_agent'],
                    payload={"query": "Analyze trading opportunity", "context": metadata},
                    dependencies=[f"{workflow_id}_market_analysis"],
                    status=WorkflowStatus.PENDING,
                    priority=2,
                    created_at=base_time
                ),
                WorkflowTask(
                    task_id=f"{workflow_id}_risk_assessment",
                    workflow_id=workflow_id,
                    task_type="risk_analysis",
                    service_endpoint=self.service_endpoints['risk_management'],
                    payload={"action": "assess_risk", "trade_data": metadata},
                    dependencies=[f"{workflow_id}_ai_recommendation"],
                    status=WorkflowStatus.PENDING,
                    priority=3,
                    created_at=base_time
                ),
                WorkflowTask(
                    task_id=f"{workflow_id}_execute_trade",
                    workflow_id=workflow_id,
                    task_type="trade_execution",
                    service_endpoint=self.service_endpoints['trading_engine'],
                    payload={"action": "execute_trade", "trade_params": metadata},
                    dependencies=[f"{workflow_id}_risk_assessment"],
                    status=WorkflowStatus.PENDING,
                    priority=4,
                    created_at=base_time
                )
            ]
            
        elif workflow_type == WorkflowType.AI_ANALYSIS:
            tasks = [
                WorkflowTask(
                    task_id=f"{workflow_id}_data_collection",
                    workflow_id=workflow_id,
                    task_type="data_collection",
                    service_endpoint=self.service_endpoints['market_data'],
                    payload={"action": "collect_data", "sources": metadata.get("sources", [])},
                    dependencies=[],
                    status=WorkflowStatus.PENDING,
                    priority=1,
                    created_at=base_time
                ),
                WorkflowTask(
                    task_id=f"{workflow_id}_ai_consensus",
                    workflow_id=workflow_id,
                    task_type="ai_consensus",
                    service_endpoint=self.service_endpoints['ai_agent'],
                    payload={"query": metadata.get("query", ""), "context": metadata},
                    dependencies=[f"{workflow_id}_data_collection"],
                    status=WorkflowStatus.PENDING,
                    priority=2,
                    created_at=base_time
                ),
                WorkflowTask(
                    task_id=f"{workflow_id}_result_validation",
                    workflow_id=workflow_id,
                    task_type="validation",
                    service_endpoint=self.service_endpoints['ai_agent'],
                    payload={"action": "validate_results", "analysis_data": metadata},
                    dependencies=[f"{workflow_id}_ai_consensus"],
                    status=WorkflowStatus.PENDING,
                    priority=3,
                    created_at=base_time
                )
            ]
            
        elif workflow_type == WorkflowType.QUANTUM_COMPUTATION:
            tasks = [
                WorkflowTask(
                    task_id=f"{workflow_id}_quantum_prep",
                    workflow_id=workflow_id,
                    task_type="quantum_preparation",
                    service_endpoint=self.service_endpoints['quantum_service'],
                    payload={"action": "prepare_quantum_job", "algorithm": metadata.get("algorithm", "")},
                    dependencies=[],
                    status=WorkflowStatus.PENDING,
                    priority=1,
                    created_at=base_time
                ),
                WorkflowTask(
                    task_id=f"{workflow_id}_quantum_execution",
                    workflow_id=workflow_id,
                    task_type="quantum_execution",
                    service_endpoint=self.service_endpoints['quantum_service'],
                    payload={"action": "execute_quantum", "job_params": metadata},
                    dependencies=[f"{workflow_id}_quantum_prep"],
                    status=WorkflowStatus.PENDING,
                    priority=2,
                    created_at=base_time
                ),
                WorkflowTask(
                    task_id=f"{workflow_id}_quantum_postprocess",
                    workflow_id=workflow_id,
                    task_type="quantum_postprocessing",
                    service_endpoint=self.service_endpoints['quantum_service'],
                    payload={"action": "postprocess_results", "quantum_results": metadata},
                    dependencies=[f"{workflow_id}_quantum_execution"],
                    status=WorkflowStatus.PENDING,
                    priority=3,
                    created_at=base_time
                )
            ]
            
        elif workflow_type == WorkflowType.COMPLIANCE_CHECK:
            tasks = [
                WorkflowTask(
                    task_id=f"{workflow_id}_kyc_check",
                    workflow_id=workflow_id,
                    task_type="kyc_verification",
                    service_endpoint=self.service_endpoints['compliance_service'],
                    payload={"action": "verify_kyc", "user_data": metadata},
                    dependencies=[],
                    status=WorkflowStatus.PENDING,
                    priority=1,
                    created_at=base_time
                ),
                WorkflowTask(
                    task_id=f"{workflow_id}_aml_screening",
                    workflow_id=workflow_id,
                    task_type="aml_screening",
                    service_endpoint=self.service_endpoints['compliance_service'],
                    payload={"action": "aml_screening", "transaction_data": metadata},
                    dependencies=[f"{workflow_id}_kyc_check"],
                    status=WorkflowStatus.PENDING,
                    priority=2,
                    created_at=base_time
                ),
                WorkflowTask(
                    task_id=f"{workflow_id}_compliance_report",
                    workflow_id=workflow_id,
                    task_type="compliance_reporting",
                    service_endpoint=self.service_endpoints['compliance_service'],
                    payload={"action": "generate_report", "compliance_data": metadata},
                    dependencies=[f"{workflow_id}_aml_screening"],
                    status=WorkflowStatus.PENDING,
                    priority=3,
                    created_at=base_time
                )
            ]
            
        elif workflow_type == WorkflowType.CYBERSECURITY_SCAN:
            tasks = [
                WorkflowTask(
                    task_id=f"{workflow_id}_threat_detection",
                    workflow_id=workflow_id,
                    task_type="threat_detection",
                    service_endpoint=self.service_endpoints['cybersecurity_service'],
                    payload={"action": "detect_threats", "scan_targets": metadata.get("targets", [])},
                    dependencies=[],
                    status=WorkflowStatus.PENDING,
                    priority=1,
                    created_at=base_time
                ),
                WorkflowTask(
                    task_id=f"{workflow_id}_vulnerability_scan",
                    workflow_id=workflow_id,
                    task_type="vulnerability_scan",
                    service_endpoint=self.service_endpoints['cybersecurity_service'],
                    payload={"action": "scan_vulnerabilities", "scan_params": metadata},
                    dependencies=[],
                    status=WorkflowStatus.PENDING,
                    priority=1,
                    created_at=base_time
                ),
                WorkflowTask(
                    task_id=f"{workflow_id}_security_report",
                    workflow_id=workflow_id,
                    task_type="security_reporting",
                    service_endpoint=self.service_endpoints['cybersecurity_service'],
                    payload={"action": "generate_security_report", "scan_results": metadata},
                    dependencies=[f"{workflow_id}_threat_detection", f"{workflow_id}_vulnerability_scan"],
                    status=WorkflowStatus.PENDING,
                    priority=2,
                    created_at=base_time
                )
            ]
        
        return tasks
    
    async def start_workflow(self, workflow_id: str) -> bool:
        """Start workflow execution"""
        if workflow_id not in self.workflows:
            logger.error(f"Workflow {workflow_id} not found")
            return False
        
        workflow = self.workflows[workflow_id]
        workflow.status = WorkflowStatus.RUNNING
        workflow.started_at = datetime.now()
        
        # Update in Redis
        await self._store_workflow(workflow)
        
        # Emit event
        await self._emit_workflow_event('workflow_started', workflow_id, {
            'workflow_type': workflow.workflow_type.value,
            'task_count': len(workflow.tasks)
        })
        
        logger.info(f"🚀 Started workflow: {workflow_id}")
        return True
    
    async def _process_workflow_queue(self):
        """Process workflow task queue"""
        logger.info("📋 Starting workflow queue processing...")
        
        while True:
            try:
                # Process all running workflows
                for workflow_id, workflow in self.workflows.items():
                    if workflow.status == WorkflowStatus.RUNNING:
                        await self._process_workflow_tasks(workflow)
                
                await asyncio.sleep(5)  # Check every 5 seconds
                
            except Exception as e:
                logger.error(f"Workflow queue processing error: {e}")
                await asyncio.sleep(10)
    
    async def _process_workflow_tasks(self, workflow: Workflow):
        """Process tasks for a specific workflow"""
        
        # Find ready tasks (dependencies completed)
        ready_tasks = []
        for task in workflow.tasks:
            if task.status == WorkflowStatus.PENDING:
                if await self._are_dependencies_completed(task, workflow):
                    ready_tasks.append(task)
        
        # Execute ready tasks
        for task in ready_tasks:
            await self._execute_task(task)
        
        # Update workflow progress
        await self._update_workflow_progress(workflow)
        
        # Check if workflow is complete
        if all(task.status in [WorkflowStatus.COMPLETED, WorkflowStatus.FAILED] for task in workflow.tasks):
            await self._complete_workflow(workflow)
    
    async def _are_dependencies_completed(self, task: WorkflowTask, workflow: Workflow) -> bool:
        """Check if task dependencies are completed"""
        if not task.dependencies:
            return True
        
        for dep_id in task.dependencies:
            dep_task = next((t for t in workflow.tasks if t.task_id == dep_id), None)
            if not dep_task or dep_task.status != WorkflowStatus.COMPLETED:
                return False
        
        return True
    
    async def _execute_task(self, task: WorkflowTask):
        """Execute a workflow task"""
        logger.info(f"🔄 Executing task: {task.task_id}")
        
        task.status = WorkflowStatus.RUNNING
        task.started_at = datetime.now()
        self.active_tasks[task.task_id] = task
        
        try:
            # Execute task based on type
            result = await self._call_service(task.service_endpoint, task.payload)
            
            task.status = WorkflowStatus.COMPLETED
            task.completed_at = datetime.now()
            task.result = result
            
            logger.info(f"✅ Task completed: {task.task_id}")
            
        except Exception as e:
            logger.error(f"❌ Task failed: {task.task_id} - {e}")
            
            task.retry_count += 1
            if task.retry_count <= task.max_retries:
                task.status = WorkflowStatus.PENDING
                logger.info(f"🔄 Retrying task: {task.task_id} (attempt {task.retry_count})")
            else:
                task.status = WorkflowStatus.FAILED
                task.error = str(e)
        
        finally:
            if task.task_id in self.active_tasks:
                del self.active_tasks[task.task_id]
    
    async def _call_service(self, endpoint: str, payload: Dict[str, Any]) -> Dict[str, Any]:
        """Call external service"""
        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"{endpoint}/workflow",
                    json=payload,
                    timeout=30.0
                )
                
                if response.status_code == 200:
                    return response.json()
                else:
                    raise Exception(f"Service call failed: {response.status_code}")
                    
        except Exception as e:
            logger.error(f"Service call error: {e}")
            raise
    
    async def _update_workflow_progress(self, workflow: Workflow):
        """Update workflow progress"""
        completed_tasks = sum(1 for task in workflow.tasks if task.status == WorkflowStatus.COMPLETED)
        total_tasks = len(workflow.tasks)
        
        workflow.progress = (completed_tasks / total_tasks) * 100 if total_tasks > 0 else 0
        
        # Update in Redis
        await self._store_workflow(workflow)
    
    async def _complete_workflow(self, workflow: Workflow):
        """Complete workflow execution"""
        failed_tasks = [task for task in workflow.tasks if task.status == WorkflowStatus.FAILED]
        
        if failed_tasks:
            workflow.status = WorkflowStatus.FAILED
            logger.error(f"❌ Workflow failed: {workflow.workflow_id} ({len(failed_tasks)} failed tasks)")
        else:
            workflow.status = WorkflowStatus.COMPLETED
            logger.info(f"✅ Workflow completed: {workflow.workflow_id}")
        
        workflow.completed_at = datetime.now()
        
        # Update in Redis
        await self._store_workflow(workflow)
        
        # Emit completion event
        await self._emit_workflow_event('workflow_completed', workflow.workflow_id, {
            'status': workflow.status.value,
            'duration': (workflow.completed_at - workflow.started_at).total_seconds() if workflow.started_at else 0,
            'failed_tasks': len(failed_tasks)
        })
    
    async def _monitor_active_workflows(self):
        """Monitor active workflows for timeouts and issues"""
        logger.info("👁️ Starting workflow monitoring...")
        
        while True:
            try:
                current_time = datetime.now()
                
                for workflow_id, workflow in self.workflows.items():
                    if workflow.status == WorkflowStatus.RUNNING:
                        # Check for timeout
                        if workflow.started_at:
                            duration = (current_time - workflow.started_at).total_seconds()
                            max_duration = workflow.estimated_duration or 3600  # 1 hour default
                            
                            if duration > max_duration:
                                logger.warning(f"⚠️ Workflow timeout: {workflow_id}")
                                await self._timeout_workflow(workflow)
                
                await asyncio.sleep(60)  # Check every minute
                
            except Exception as e:
                logger.error(f"Workflow monitoring error: {e}")
                await asyncio.sleep(60)
    
    async def _timeout_workflow(self, workflow: Workflow):
        """Handle workflow timeout"""
        workflow.status = WorkflowStatus.FAILED
        workflow.completed_at = datetime.now()
        
        # Cancel active tasks
        for task in workflow.tasks:
            if task.status == WorkflowStatus.RUNNING:
                task.status = WorkflowStatus.CANCELLED
                task.error = "Workflow timeout"
        
        await self._store_workflow(workflow)
        
        await self._emit_workflow_event('workflow_timeout', workflow.workflow_id, {
            'duration': (workflow.completed_at - workflow.started_at).total_seconds() if workflow.started_at else 0
        })
    
    async def _process_kafka_events(self):
        """Process incoming Kafka events"""
        logger.info("📨 Starting Kafka event processing...")
        
        while True:
            try:
                message_pack = self.kafka_consumer.poll(timeout_ms=1000)
                
                for topic_partition, messages in message_pack.items():
                    for message in messages:
                        event_data = message.value
                        await self._handle_external_event(event_data)
                        
            except Exception as e:
                logger.error(f"Kafka event processing error: {e}")
                await asyncio.sleep(5)
    
    async def _handle_external_event(self, event_data: Dict[str, Any]):
        """Handle external events that might trigger workflows"""
        event_type = event_data.get('event_type', '')
        
        # Auto-trigger workflows based on events
        if event_type == 'market_volatility_spike':
            await self.create_workflow(
                WorkflowType.TRADING,
                "Market Volatility Response",
                "Automated response to market volatility",
                "system",
                event_data.get('data', {})
            )
        
        elif event_type == 'security_threat_detected':
            await self.create_workflow(
                WorkflowType.CYBERSECURITY_SCAN,
                "Security Threat Response",
                "Automated security threat response",
                "system",
                event_data.get('data', {})
            )
        
        elif event_type == 'compliance_check_required':
            await self.create_workflow(
                WorkflowType.COMPLIANCE_CHECK,
                "Compliance Verification",
                "Automated compliance check",
                "system",
                event_data.get('data', {})
            )
    
    async def _health_check_services(self):
        """Health check all registered services"""
        logger.info("🏥 Starting service health checks...")
        
        while True:
            try:
                for service_name, endpoint in self.service_endpoints.items():
                    try:
                        async with httpx.AsyncClient() as client:
                            response = await client.get(f"{endpoint}/health", timeout=5.0)
                            
                            if response.status_code == 200:
                                self.service_registry[service_name] = {
                                    'status': 'healthy',
                                    'last_check': datetime.now().isoformat()
                                }
                            else:
                                self.service_registry[service_name] = {
                                    'status': 'unhealthy',
                                    'last_check': datetime.now().isoformat(),
                                    'error': f"HTTP {response.status_code}"
                                }
                    except Exception as e:
                        self.service_registry[service_name] = {
                            'status': 'unhealthy',
                            'last_check': datetime.now().isoformat(),
                            'error': str(e)
                        }
                
                await asyncio.sleep(30)  # Check every 30 seconds
                
            except Exception as e:
                logger.error(f"Health check error: {e}")
                await asyncio.sleep(60)
    
    async def _websocket_server(self):
        """WebSocket server for real-time updates"""
        logger.info("🔌 Starting WebSocket server...")
        
        async def handle_websocket(websocket, path):
            self.websocket_connections.add(websocket)
            try:
                await websocket.wait_closed()
            finally:
                self.websocket_connections.remove(websocket)
        
        try:
            await websockets.serve(handle_websocket, "localhost", 8027)
            logger.info("✅ WebSocket server started on ws://localhost:8027")
            
            await asyncio.Future()  # Run forever
            
        except Exception as e:
            logger.error(f"WebSocket server error: {e}")
    
    async def _periodic_cleanup(self):
        """Periodic cleanup of completed workflows"""
        logger.info("🧹 Starting periodic cleanup...")
        
        while True:
            try:
                current_time = datetime.now()
                cleanup_threshold = current_time - timedelta(hours=24)  # Keep for 24 hours
                
                workflows_to_remove = []
                for workflow_id, workflow in self.workflows.items():
                    if (workflow.status in [WorkflowStatus.COMPLETED, WorkflowStatus.FAILED] and
                        workflow.completed_at and workflow.completed_at < cleanup_threshold):
                        workflows_to_remove.append(workflow_id)
                
                for workflow_id in workflows_to_remove:
                    del self.workflows[workflow_id]
                    # Remove from Redis
                    self.redis_client.delete(f"workflow:{workflow_id}")
                
                if workflows_to_remove:
                    logger.info(f"🧹 Cleaned up {len(workflows_to_remove)} old workflows")
                
                await asyncio.sleep(3600)  # Cleanup every hour
                
            except Exception as e:
                logger.error(f"Cleanup error: {e}")
                await asyncio.sleep(3600)
    
    async def _store_workflow(self, workflow: Workflow):
        """Store workflow in Redis"""
        try:
            workflow_data = asdict(workflow)
            self.redis_client.setex(
                f"workflow:{workflow.workflow_id}",
                86400,  # 24 hours TTL
                json.dumps(workflow_data, default=str)
            )
        except Exception as e:
            logger.error(f"Failed to store workflow: {e}")
    
    async def _emit_workflow_event(self, event_type: str, workflow_id: str, data: Dict[str, Any]):
        """Emit workflow event"""
        try:
            event = {
                'event_type': event_type,
                'service': 'backend_orchestrator',
                'workflow_id': workflow_id,
                'timestamp': datetime.now().isoformat(),
                'data': data
            }
            
            # Emit to Kafka
            self.kafka_producer.send('workflow_events', event)
            
            # Broadcast to WebSocket connections
            await self._broadcast_to_websockets(event)
            
        except Exception as e:
            logger.error(f"Failed to emit workflow event: {e}")
    
    async def _broadcast_to_websockets(self, message: Dict[str, Any]):
        """Broadcast message to WebSocket connections"""
        if not self.websocket_connections:
            return
        
        message_json = json.dumps(message, default=str)
        
        disconnected = set()
        for websocket in self.websocket_connections:
            try:
                await websocket.send(message_json)
            except Exception:
                disconnected.add(websocket)
        
        self.websocket_connections -= disconnected
    
    # Public API methods
    def get_workflow_status(self, workflow_id: str) -> Optional[Dict[str, Any]]:
        """Get workflow status"""
        if workflow_id not in self.workflows:
            return None
        
        workflow = self.workflows[workflow_id]
        return {
            'workflow_id': workflow_id,
            'status': workflow.status.value,
            'progress': workflow.progress,
            'created_at': workflow.created_at.isoformat(),
            'started_at': workflow.started_at.isoformat() if workflow.started_at else None,
            'completed_at': workflow.completed_at.isoformat() if workflow.completed_at else None,
            'task_count': len(workflow.tasks),
            'completed_tasks': len([t for t in workflow.tasks if t.status == WorkflowStatus.COMPLETED]),
            'failed_tasks': len([t for t in workflow.tasks if t.status == WorkflowStatus.FAILED])
        }
    
    def get_all_workflows(self) -> List[Dict[str, Any]]:
        """Get all workflows"""
        return [self.get_workflow_status(wf_id) for wf_id in self.workflows.keys()]
    
    def get_service_health(self) -> Dict[str, Any]:
        """Get service health status"""
        return {
            'services': self.service_registry,
            'total_services': len(self.service_endpoints),
            'healthy_services': len([s for s in self.service_registry.values() if s.get('status') == 'healthy']),
            'timestamp': datetime.now().isoformat()
        }
    
    async def cancel_workflow(self, workflow_id: str) -> bool:
        """Cancel a workflow"""
        if workflow_id not in self.workflows:
            return False
        
        workflow = self.workflows[workflow_id]
        workflow.status = WorkflowStatus.CANCELLED
        workflow.completed_at = datetime.now()
        
        # Cancel active tasks
        for task in workflow.tasks:
            if task.status in [WorkflowStatus.PENDING, WorkflowStatus.RUNNING]:
                task.status = WorkflowStatus.CANCELLED
        
        await self._store_workflow(workflow)
        
        await self._emit_workflow_event('workflow_cancelled', workflow_id, {
            'cancelled_by': 'user'
        })
        
        return True

# Main execution
async def main():
    """Main execution function"""
    logger.info("🚀 Starting Kontour Backend Orchestrator...")
    
    try:
        orchestrator = KontourBackendOrchestrator()
        await orchestrator.start_orchestrator()
        
    except KeyboardInterrupt:
        logger.info("👋 Shutting down Backend Orchestrator...")
    except Exception as e:
        logger.error(f"❌ Fatal error: {e}")
        raise

if __name__ == "__main__":
    asyncio.run(main())
