@echo off
setlocal enabledelayedexpansion
title Kontour Coin AI Agent Service
color 0A

echo.
echo ████████████████████████████████████████████████████████████████
echo ██                                                            ██
echo ██    🤖 KONTOUR COIN AI AGENT SERVICE LAUNCHER 🤖            ██
echo ██                                                            ██
echo ██    Multi-LLM AI Agent System for Cryptocurrency           ██
echo ██    GPT-4 • Claude • Gemini • DeepSeek • Local Models     ██
echo ██                                                            ██
echo ████████████████████████████████████████████████████████████████
echo.

:: Set environment variables
set PYTHONPATH=%CD%
set SERVICE_NAME=ai-agent-service
set SERVICE_PORT=8025

echo 🔍 Checking Prerequisites...
echo ===============================================

:: Check Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python not found. Please install Python 3.11+
    pause
    exit /b 1
) else (
    for /f "tokens=*" %%i in ('python --version') do set PYTHON_VERSION=%%i
    echo ✅ Python found: !PYTHON_VERSION!
)

:: Check pip
pip --version >nul 2>&1
if errorlevel 1 (
    echo ❌ pip not found
    pause
    exit /b 1
) else (
    echo ✅ pip found
)

echo.
echo 📦 Installing Dependencies...
echo ===============================================

:: Install requirements
if exist "requirements.txt" (
    echo Installing Python packages...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo ❌ Failed to install dependencies
        pause
        exit /b 1
    ) else (
        echo ✅ Dependencies installed successfully
    )
) else (
    echo ⚠️  requirements.txt not found, installing basic packages...
    pip install fastapi uvicorn openai anthropic google-generativeai transformers torch redis kafka-python httpx websockets python-dotenv
)

echo.
echo 🔧 Environment Configuration...
echo ===============================================

:: Check for .env file
if not exist ".env" (
    echo Creating .env template...
    (
        echo # Kontour Coin AI Agent Service Configuration
        echo.
        echo # OpenAI Configuration
        echo OPENAI_API_KEY=your_openai_api_key_here
        echo.
        echo # Anthropic Claude Configuration
        echo ANTHROPIC_API_KEY=your_anthropic_api_key_here
        echo.
        echo # Google Gemini Configuration
        echo GOOGLE_API_KEY=your_google_api_key_here
        echo.
        echo # DeepSeek Configuration
        echo DEEPSEEK_API_KEY=your_deepseek_api_key_here
        echo.
        echo # Platform Integration
        echo REDIS_HOST=localhost
        echo REDIS_PORT=6379
        echo KAFKA_BOOTSTRAP_SERVERS=localhost:9092
        echo.
        echo # Service Configuration
        echo SERVICE_HOST=0.0.0.0
        echo SERVICE_PORT=8025
        echo LOG_LEVEL=INFO
        echo.
        echo # Model Configuration
        echo DEFAULT_TEMPERATURE=0.7
        echo DEFAULT_MAX_TOKENS=1000
        echo ENABLE_LOCAL_MODELS=false
    ) > .env
    echo ✅ .env template created
    echo ⚠️  Please configure your API keys in .env file
) else (
    echo ✅ .env file found
)

echo.
echo 🚀 Starting AI Agent Service...
echo ===============================================

:: Check if port is available
netstat -an | findstr ":8025 " >nul 2>&1
if not errorlevel 1 (
    echo ⚠️  Port 8025 is already in use
    echo Attempting to start on alternative port...
    set SERVICE_PORT=8026
)

echo Service will start on port: !SERVICE_PORT!
echo.

:: Start the service
echo 🤖 Launching Kontour AI Agent Service...
echo.
echo Service Information:
echo   • Service URL: http://localhost:!SERVICE_PORT!
echo   • Health Check: http://localhost:!SERVICE_PORT!/health
echo   • API Documentation: http://localhost:!SERVICE_PORT!/docs
echo   • WebSocket: ws://localhost:8026
echo.
echo Available AI Models:
echo   • OpenAI GPT-4 (Premium reasoning)
echo   • OpenAI GPT-3.5 (Fast responses)
echo   • Anthropic Claude 3 (Analysis expert)
echo   • Google Gemini Pro (Multimodal)
echo   • DeepSeek (Technical specialist)
echo   • Local LLaMA (Privacy-focused)
echo.
echo Agent Specializations:
echo   • Trading Advisor
echo   • Market Analyst
echo   • Risk Manager
echo   • Portfolio Optimizer
echo   • News Analyst
echo   • Technical Analyst
echo   • Sentiment Analyzer
echo   • Blockchain Monitor
echo.

:: Start the main service
python ai_agent_service.py

:: If service exits, show status
echo.
if errorlevel 1 (
    echo ❌ AI Agent Service exited with error
    echo.
    echo 🔧 Troubleshooting:
    echo   • Check API keys in .env file
    echo   • Ensure Redis is running on localhost:6379
    echo   • Ensure Kafka is running on localhost:9092
    echo   • Check Python dependencies are installed
    echo   • Verify port 8025 is available
    echo.
) else (
    echo ✅ AI Agent Service stopped gracefully
)

echo.
echo 📊 Service Status:
echo   • Service Name: !SERVICE_NAME!
echo   • Port: !SERVICE_PORT!
echo   • Status: Stopped
echo.

echo Press any key to exit...
pause >nul
