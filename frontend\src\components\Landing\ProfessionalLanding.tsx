import React from 'react';
import {
  Box,
  Container,
  Typography,
  Button,
  Grid,
  Card,
  CardContent,
  Avatar,
  Chip,
  useTheme,
  alpha,
  Stack
} from '@mui/material';
import {
  TrendingUp as TradingIcon,
  Psychology as AIIcon,
  Science as QuantumIcon,
  Security as SecurityIcon,
  AccountBalanceWallet as WalletIcon,
  Timeline as AnalyticsIcon,
  Biotech as GenomicsIcon,
  Gavel as ComplianceIcon,
  AutoAwesome as AutoIcon,
  Rocket as RocketIcon,
  Shield as ShieldIcon,
  Speed as SpeedIcon
} from '@mui/icons-material';
import { styled } from '@mui/material/styles';
import { useNavigate } from 'react-router-dom';

// Styled Components
const HeroSection = styled(Box)(({ theme }) => ({
  background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.1)} 0%, ${alpha(theme.palette.secondary.main, 0.1)} 100%)`,
  minHeight: '100vh',
  display: 'flex',
  alignItems: 'center',
  position: 'relative',
  overflow: 'hidden',
  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    background: `radial-gradient(circle at 20% 80%, ${alpha(theme.palette.primary.main, 0.15)} 0%, transparent 50%),
                 radial-gradient(circle at 80% 20%, ${alpha(theme.palette.secondary.main, 0.15)} 0%, transparent 50%)`,
    zIndex: -1,
  },
}));

const FeatureCard = styled(Card)(({ theme }) => ({
  height: '100%',
  background: alpha(theme.palette.background.paper, 0.8),
  backdropFilter: 'blur(20px)',
  border: `1px solid ${alpha(theme.palette.divider, 0.12)}`,
  borderRadius: 20,
  transition: 'all 0.3s ease-in-out',
  '&:hover': {
    transform: 'translateY(-8px)',
    boxShadow: `0 20px 40px ${alpha(theme.palette.primary.main, 0.15)}`,
    border: `1px solid ${alpha(theme.palette.primary.main, 0.3)}`,
  },
}));

const StatsCard = styled(Card)(({ theme }) => ({
  background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.primary.dark} 100%)`,
  color: theme.palette.primary.contrastText,
  borderRadius: 16,
  padding: theme.spacing(3),
  textAlign: 'center',
}));

const ProfessionalLanding: React.FC = () => {
  const theme = useTheme();
  const navigate = useNavigate();

  const features = [
    {
      icon: <TradingIcon />,
      title: 'Advanced Trading',
      description: 'Professional trading interface with AI-powered insights and real-time market analysis',
      color: theme.palette.success.main,
      path: '/trading'
    },
    {
      icon: <AIIcon />,
      title: 'AI Agents',
      description: 'Multi-LLM AI agents providing consensus-based trading recommendations and market analysis',
      color: theme.palette.primary.main,
      path: '/ai-agents'
    },
    {
      icon: <QuantumIcon />,
      title: 'Quantum Computing',
      description: 'Quantum-enhanced algorithms for portfolio optimization and risk management',
      color: theme.palette.info.main,
      path: '/quantum'
    },
    {
      icon: <SecurityIcon />,
      title: 'Enterprise Security',
      description: 'Quantum-resistant cryptography and advanced cybersecurity monitoring',
      color: theme.palette.error.main,
      path: '/security'
    },
    {
      icon: <WalletIcon />,
      title: 'Smart Wallet',
      description: 'Multi-chain wallet with DeFi integration and automated portfolio management',
      color: theme.palette.secondary.main,
      path: '/wallet'
    },
    {
      icon: <AnalyticsIcon />,
      title: 'Advanced Analytics',
      description: 'Real-time market analytics with predictive modeling and risk assessment',
      color: theme.palette.warning.main,
      path: '/analytics'
    },
    {
      icon: <GenomicsIcon />,
      title: 'Genomics DeFi',
      description: 'Secure genomic data tokenization with privacy-preserving analytics',
      color: theme.palette.success.dark,
      path: '/genomics'
    },
    {
      icon: <ComplianceIcon />,
      title: 'Compliance Suite',
      description: 'Automated KYC/AML compliance with regulatory reporting and monitoring',
      color: theme.palette.info.dark,
      path: '/compliance'
    }
  ];

  const stats = [
    { label: 'Total Value Locked', value: '$2.4B+', icon: <AutoIcon /> },
    { label: 'Active Users', value: '150K+', icon: <RocketIcon /> },
    { label: 'Transactions/Day', value: '1M+', icon: <SpeedIcon /> },
    { label: 'Security Score', value: '99.9%', icon: <ShieldIcon /> }
  ];

  const handleGetStarted = () => {
    navigate('/dashboard');
  };

  const handleExploreFeature = (path: string) => {
    navigate(path);
  };

  return (
    <Box>
      {/* Hero Section */}
      <HeroSection>
        <Container maxWidth="lg">
          <Grid container spacing={6} alignItems="center">
            <Grid item xs={12} md={6}>
              <Box sx={{ mb: 4 }}>
                <Typography
                  variant="h1"
                  sx={{
                    fontWeight: 800,
                    background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`,
                    backgroundClip: 'text',
                    WebkitBackgroundClip: 'text',
                    WebkitTextFillColor: 'transparent',
                    mb: 3
                  }}
                >
                  💎 Kontour Coin
                </Typography>
                <Typography variant="h4" sx={{ mb: 3, fontWeight: 600, color: 'text.primary' }}>
                  The Future of Cryptocurrency Trading
                </Typography>
                <Typography variant="h6" sx={{ mb: 4, color: 'text.secondary', lineHeight: 1.6 }}>
                  Advanced AI-powered trading platform with quantum computing, genomics DeFi, 
                  and enterprise-grade security. Experience the next generation of digital finance.
                </Typography>
                <Stack direction="row" spacing={2}>
                  <Button
                    variant="contained"
                    size="large"
                    onClick={handleGetStarted}
                    sx={{
                      px: 4,
                      py: 1.5,
                      fontSize: '1.1rem',
                      background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.primary.dark} 100%)`,
                    }}
                  >
                    Get Started
                  </Button>
                  <Button
                    variant="outlined"
                    size="large"
                    onClick={() => handleExploreFeature('/trading')}
                    sx={{ px: 4, py: 1.5, fontSize: '1.1rem' }}
                  >
                    Explore Trading
                  </Button>
                </Stack>
              </Box>
            </Grid>
            <Grid item xs={12} md={6}>
              <Box sx={{ position: 'relative' }}>
                <Card
                  sx={{
                    p: 4,
                    background: alpha(theme.palette.background.paper, 0.9),
                    backdropFilter: 'blur(20px)',
                    border: `1px solid ${alpha(theme.palette.primary.main, 0.2)}`,
                    borderRadius: 4,
                  }}
                >
                  <Typography variant="h6" gutterBottom fontWeight={600}>
                    Live Platform Stats
                  </Typography>
                  <Grid container spacing={2}>
                    {stats.map((stat, index) => (
                      <Grid item xs={6} key={index}>
                        <Box sx={{ textAlign: 'center', p: 2 }}>
                          <Avatar
                            sx={{
                              bgcolor: theme.palette.primary.main,
                              mx: 'auto',
                              mb: 1,
                              width: 48,
                              height: 48
                            }}
                          >
                            {stat.icon}
                          </Avatar>
                          <Typography variant="h5" fontWeight={700} color="primary">
                            {stat.value}
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            {stat.label}
                          </Typography>
                        </Box>
                      </Grid>
                    ))}
                  </Grid>
                </Card>
              </Box>
            </Grid>
          </Grid>
        </Container>
      </HeroSection>

      {/* Features Section */}
      <Box sx={{ py: 10, bgcolor: 'background.default' }}>
        <Container maxWidth="lg">
          <Box sx={{ textAlign: 'center', mb: 8 }}>
            <Typography variant="h2" fontWeight={700} gutterBottom>
              Powerful Features
            </Typography>
            <Typography variant="h6" color="text.secondary" sx={{ maxWidth: 600, mx: 'auto' }}>
              Discover the advanced capabilities that make Kontour Coin the most sophisticated 
              cryptocurrency platform available today.
            </Typography>
          </Box>

          <Grid container spacing={4}>
            {features.map((feature, index) => (
              <Grid item xs={12} sm={6} md={3} key={index}>
                <FeatureCard onClick={() => handleExploreFeature(feature.path)} sx={{ cursor: 'pointer' }}>
                  <CardContent sx={{ p: 3, textAlign: 'center' }}>
                    <Avatar
                      sx={{
                        bgcolor: feature.color,
                        width: 64,
                        height: 64,
                        mx: 'auto',
                        mb: 2
                      }}
                    >
                      {feature.icon}
                    </Avatar>
                    <Typography variant="h6" fontWeight={600} gutterBottom>
                      {feature.title}
                    </Typography>
                    <Typography variant="body2" color="text.secondary" sx={{ lineHeight: 1.6 }}>
                      {feature.description}
                    </Typography>
                  </CardContent>
                </FeatureCard>
              </Grid>
            ))}
          </Grid>
        </Container>
      </Box>

      {/* Technology Stack */}
      <Box sx={{ py: 10, bgcolor: alpha(theme.palette.primary.main, 0.02) }}>
        <Container maxWidth="lg">
          <Box sx={{ textAlign: 'center', mb: 8 }}>
            <Typography variant="h2" fontWeight={700} gutterBottom>
              Advanced Technology Stack
            </Typography>
            <Typography variant="h6" color="text.secondary">
              Built with cutting-edge technologies for maximum performance and security
            </Typography>
          </Box>

          <Grid container spacing={4}>
            <Grid item xs={12} md={4}>
              <Card sx={{ p: 4, textAlign: 'center', height: '100%' }}>
                <Typography variant="h5" fontWeight={600} gutterBottom color="primary">
                  AI & Machine Learning
                </Typography>
                <Stack spacing={1} sx={{ mt: 2 }}>
                  <Chip label="GPT-4 Integration" variant="outlined" />
                  <Chip label="Claude 3 Analysis" variant="outlined" />
                  <Chip label="Gemini Pro Insights" variant="outlined" />
                  <Chip label="DeepSeek Technical" variant="outlined" />
                  <Chip label="Multi-Agent Consensus" variant="outlined" />
                </Stack>
              </Card>
            </Grid>
            <Grid item xs={12} md={4}>
              <Card sx={{ p: 4, textAlign: 'center', height: '100%' }}>
                <Typography variant="h5" fontWeight={600} gutterBottom color="primary">
                  Quantum Computing
                </Typography>
                <Stack spacing={1} sx={{ mt: 2 }}>
                  <Chip label="Quantum Algorithms" variant="outlined" />
                  <Chip label="Portfolio Optimization" variant="outlined" />
                  <Chip label="Risk Assessment" variant="outlined" />
                  <Chip label="Cryptographic Security" variant="outlined" />
                  <Chip label="Market Prediction" variant="outlined" />
                </Stack>
              </Card>
            </Grid>
            <Grid item xs={12} md={4}>
              <Card sx={{ p: 4, textAlign: 'center', height: '100%' }}>
                <Typography variant="h5" fontWeight={600} gutterBottom color="primary">
                  Blockchain & DeFi
                </Typography>
                <Stack spacing={1} sx={{ mt: 2 }}>
                  <Chip label="Multi-Chain Support" variant="outlined" />
                  <Chip label="Smart Contracts" variant="outlined" />
                  <Chip label="DeFi Protocols" variant="outlined" />
                  <Chip label="Cross-Chain Bridges" variant="outlined" />
                  <Chip label="Yield Farming" variant="outlined" />
                </Stack>
              </Card>
            </Grid>
          </Grid>
        </Container>
      </Box>

      {/* Call to Action */}
      <Box sx={{ py: 10, bgcolor: 'background.paper' }}>
        <Container maxWidth="md">
          <Box sx={{ textAlign: 'center' }}>
            <Typography variant="h2" fontWeight={700} gutterBottom>
              Ready to Start Trading?
            </Typography>
            <Typography variant="h6" color="text.secondary" sx={{ mb: 4 }}>
              Join thousands of traders who trust Kontour Coin for their cryptocurrency investments
            </Typography>
            <Stack direction="row" spacing={2} justifyContent="center">
              <Button
                variant="contained"
                size="large"
                onClick={handleGetStarted}
                sx={{
                  px: 6,
                  py: 2,
                  fontSize: '1.2rem',
                  background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.primary.dark} 100%)`,
                }}
              >
                Launch Platform
              </Button>
              <Button
                variant="outlined"
                size="large"
                onClick={() => handleExploreFeature('/ai-agents')}
                sx={{ px: 6, py: 2, fontSize: '1.2rem' }}
              >
                Try AI Agents
              </Button>
            </Stack>
          </Box>
        </Container>
      </Box>
    </Box>
  );
};

export default ProfessionalLanding;
